// @ts-ignore - 抑制 vite 导入警告
import { defineConfig } from 'vite';
// @ts-ignore - 抑制 react 插件导入警告
import react from '@vitejs/plugin-react';
// @ts-ignore - 抑制 svgr 插件导入警告
import svgrPlugin from '@arco-plugins/vite-plugin-svgr';
// @ts-ignore - 抑制 arco 插件导入警告
import vitePluginForArco from '@arco-plugins/vite-react';
// @ts-ignore - 抑制 settings 导入警告
import setting from './src/settings.json';

// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    alias: [{ find: '@', replacement: '/src' }],
  },
  plugins: [
    react(),
    svgrPlugin({
      svgrOptions: {},
    }),
    vitePluginForArco({
      theme: '@arco-themes/react-arco-pro',
      modifyVars: {
        'arcoblue-6': setting.themeColor,
      },
    }),
  ],
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
  // 设置资源引用路径
  base: '/',
  // 构建配置
  build: {
    outDir: 'dist',
    emptyOutDir: true,
  },
  // 开发服务器代理配置
  server: {
    proxy: {
      '/adm': {
        target: 'http://localhost:8080', // 后端服务器地址
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/adm/, '/adm'),
      },
      '/api': {
        target: 'http://localhost:8080', // 后端服务器地址
        changeOrigin: true,
        secure: false,
      },
    },
  },
});
