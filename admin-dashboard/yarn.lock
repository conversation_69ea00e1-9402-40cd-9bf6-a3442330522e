# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  "integrity" "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw=="
  "resolved" "https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@antv/adjust@^0.2.1":
  "integrity" "sha512-rihqcCdS7piQnK1nRlCvbIaj2QeaqghxINXiMpTJp+0c9cKlTUwL7/2r+gv9YN5R0P1WzSHTmK2Sn+bQCJDo0Q=="
  "resolved" "https://registry.npmjs.org/@antv/adjust/-/adjust-0.2.3.tgz"
  "version" "0.2.3"
  dependencies:
    "@antv/util" "~2.0.0"
    "tslib" "^1.10.0"

"@antv/attr@^0.3.1":
  "integrity" "sha512-31PfcVKeQdPBmr/QD+IC0NB/FbdtVKOXBCNMepFc5/dEs7jphmgG1V4tfAJmcXIHubCTHOjpscTrDIvoKSGvMQ=="
  "resolved" "https://registry.npmjs.org/@antv/attr/-/attr-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "@antv/color-util" "^2.0.1"
    "@antv/util" "~2.0.0"
    "tslib" "^1.10.0"

"@antv/color-util@^2.0.1", "@antv/color-util@^2.0.2", "@antv/color-util@^2.0.3":
  "integrity" "sha512-KnPEaAH+XNJMjax9U35W67nzPI+QQ2x27pYlzmSIWrbj4/k8PGrARXfzDTjwoozHJY8qG62Z+Ww6Alhu2FctXQ=="
  "resolved" "https://registry.npmjs.org/@antv/color-util/-/color-util-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "@antv/util" "^2.0.9"
    "tslib" "^2.0.3"

"@antv/component@*", "@antv/component@^0.8.7":
  "integrity" "sha512-IaxnxS18GkbCJEpQBulVNHqreXeGsi9lIh7Es5DKm7QW61NxUmd/F25n3MneEEsZuOn/uTIT2HSROxxewqWN3w=="
  "resolved" "https://registry.npmjs.org/@antv/component/-/component-0.8.20.tgz"
  "version" "0.8.20"
  dependencies:
    "@antv/color-util" "^2.0.3"
    "@antv/dom-util" "~2.0.1"
    "@antv/g-base" "0.5.6"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.7"
    "@antv/scale" "~0.3.1"
    "@antv/util" "~2.0.0"
    "fecha" "~4.2.0"
    "tslib" "^2.0.3"

"@antv/coord@^0.3.0":
  "integrity" "sha512-rFE94C8Xzbx4xmZnHh2AnlB3Qm1n5x0VT3OROy257IH6Rm4cuzv1+tZaUBATviwZd99S+rOY9telw/+6C9GbRw=="
  "resolved" "https://registry.npmjs.org/@antv/coord/-/coord-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "@antv/matrix-util" "^3.1.0-beta.2"
    "@antv/util" "~2.0.12"
    "tslib" "^2.1.0"

"@antv/data-set@^0.11.8":
  "integrity" "sha512-8/YDsfk4wNQdo/J9tfmzOuo9Y5nl0mB+sSZO+tEZsHFLUhMrioJGBMPkuW51Pn0zcVZPNivuMBi2sQKYCpCeew=="
  "resolved" "https://registry.npmjs.org/@antv/data-set/-/data-set-0.11.8.tgz"
  "version" "0.11.8"
  dependencies:
    "@antv/hierarchy" "^0.6.0"
    "@antv/util" "^2.0.0"
    "d3-composite-projections" "^1.2.0"
    "d3-dsv" "^1.0.5"
    "d3-geo" "~1.6.4"
    "d3-geo-projection" "~2.1.2"
    "d3-hexjson" "^1.0.1"
    "d3-hierarchy" "^1.1.5"
    "d3-sankey" "^0.9.1"
    "d3-voronoi" "^1.1.2"
    "dagre" "^0.8.2"
    "point-at-length" "^1.0.2"
    "regression" "^2.0.0"
    "simple-statistics" "^6.1.0"
    "topojson-client" "^3.0.0"
    "wolfy87-eventemitter" "^5.1.0"

"@antv/dom-util@^2.0.2", "@antv/dom-util@~2.0.1":
  "integrity" "sha512-dUHsUT4U9X1T1/Y9bH3jRMe0MzvWJk2jSQm1vm3w9NX+Ra0ftq5VUBiGTNbthm3nFwG0fFFjip904rYjl50g4A=="
  "resolved" "https://registry.npmjs.org/@antv/dom-util/-/dom-util-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "tslib" "^2.0.3"

"@antv/event-emitter@^0.1.1", "@antv/event-emitter@^0.1.2", "@antv/event-emitter@~0.1.0":
  "integrity" "sha512-6C6NJOdoNVptCr5y9BVOhKkCgW7LFs/SpcRyAExUeSjAm0zJqcqNkSIRGsXYhj4PJI+CZICHzGwwiSnIsE68Ug=="
  "resolved" "https://registry.npmjs.org/@antv/event-emitter/-/event-emitter-0.1.2.tgz"
  "version" "0.1.2"

"@antv/g-base@^0.5.3", "@antv/g-base@~0.5.6", "@antv/g-base@0.5.6":
  "integrity" "sha512-szxqFQ/xdCnfaeSEEC2kVjXdKxJnvKKJNT0MvaOG3UXOfsjPDLgb3IKLr+bU3sLvTAQfPhsbtYh7mWb03+mGjA=="
  "resolved" "https://registry.npmjs.org/@antv/g-base/-/g-base-0.5.6.tgz"
  "version" "0.5.6"
  dependencies:
    "@antv/event-emitter" "^0.1.1"
    "@antv/g-math" "^0.1.6"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.5"
    "@antv/util" "~2.0.0"
    "@types/d3-timer" "^2.0.0"
    "d3-ease" "^1.0.5"
    "d3-interpolate" "^1.3.2"
    "d3-timer" "^1.0.9"
    "detect-browser" "^5.1.0"
    "tslib" "^2.0.3"

"@antv/g-canvas@~0.5.10":
  "integrity" "sha512-iJ/muwwqCCNONVlPIzv/7OL5iLguaKRj2BxNMytUO3TWwamM+kHkiyYEOkS0dPn9h/hBsHYlLUluSVz2Fp6/bw=="
  "resolved" "https://registry.npmjs.org/@antv/g-canvas/-/g-canvas-0.5.12.tgz"
  "version" "0.5.12"
  dependencies:
    "@antv/g-base" "^0.5.3"
    "@antv/g-math" "^0.1.6"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.5"
    "@antv/util" "~2.0.0"
    "gl-matrix" "^3.0.0"
    "tslib" "^2.0.3"

"@antv/g-math@^0.1.6":
  "integrity" "sha512-xGyXaloD1ynfp7gS4VuV+MjSptZIwHvLHr8ekXJSFAeWPYLu84yOW2wOZHDdp1bzDAIuRv6xDBW58YGHrWsFcA=="
  "resolved" "https://registry.npmjs.org/@antv/g-math/-/g-math-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "@antv/util" "~2.0.0"
    "gl-matrix" "^3.0.0"

"@antv/g-svg@~0.5.6":
  "integrity" "sha512-Xve1EUGk4HMbl2nq4ozR4QLh6GyoZ8Xw/+9kHYI4B5P2lIUQU95MuRsaLFfW5NNpZDx85ZeH97tqEmC9L96E7A=="
  "resolved" "https://registry.npmjs.org/@antv/g-svg/-/g-svg-0.5.6.tgz"
  "version" "0.5.6"
  dependencies:
    "@antv/g-base" "^0.5.3"
    "@antv/g-math" "^0.1.6"
    "@antv/util" "~2.0.0"
    "detect-browser" "^5.0.0"
    "tslib" "^2.0.3"

"@antv/g2@^4.1.26", "@antv/g2@4.1.30":
  "integrity" "sha512-AS4Q8IOgWRyfSJrQQ/3oxZEWS0w3vKuBheirQubn5E4JV9GtWN14SELmfBHepnYXX/W3gJ5k1oSwZ2zd+Oqf9A=="
  "resolved" "https://registry.npmjs.org/@antv/g2/-/g2-4.1.30.tgz"
  "version" "4.1.30"
  dependencies:
    "@antv/adjust" "^0.2.1"
    "@antv/attr" "^0.3.1"
    "@antv/color-util" "^2.0.2"
    "@antv/component" "^0.8.7"
    "@antv/coord" "^0.3.0"
    "@antv/dom-util" "^2.0.2"
    "@antv/event-emitter" "~0.1.0"
    "@antv/g-base" "~0.5.6"
    "@antv/g-canvas" "~0.5.10"
    "@antv/g-svg" "~0.5.6"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "^2.0.3"
    "@antv/scale" "^0.3.7"
    "@antv/util" "~2.0.5"
    "tslib" "^2.0.0"

"@antv/g2plot@2.3.35":
  "integrity" "sha512-jSfZztjYR6Xyx+d0fqu+JW7BkrcAm24CSb9fWvZC/ZnnWHTyFbN4qOb+wF8fRo5hPtfnnL1bwsOjdqNSVN1Tdg=="
  "resolved" "https://registry.npmjs.org/@antv/g2plot/-/g2plot-2.3.35.tgz"
  "version" "2.3.35"
  dependencies:
    "@antv/event-emitter" "^0.1.2"
    "@antv/g2" "^4.1.26"
    "d3-hierarchy" "^2.0.0"
    "d3-regression" "^1.3.5"
    "fmin" "^0.0.2"
    "pdfast" "^0.2.0"
    "size-sensor" "^1.0.1"
    "tslib" "^2.0.3"

"@antv/hierarchy@^0.6.0":
  "integrity" "sha512-wVzUl+pxny5gyGJ2mkWx8IiEypX6bnMHgr/NILgbxY6shoy0Vf4FhZpI3CY8Ez7bQT6js8fMkB2NymPW7d7i8A=="
  "resolved" "https://registry.npmjs.org/@antv/hierarchy/-/hierarchy-0.6.8.tgz"
  "version" "0.6.8"
  dependencies:
    "@antv/util" "^2.0.7"

"@antv/matrix-util@^3.0.4":
  "integrity" "sha512-BAPyu6dUliHcQ7fm9hZSGKqkwcjEDVLVAstlHULLvcMZvANHeLXgHEgV7JqcAV/GIhIz8aZChIlzM1ZboiXpYQ=="
  "resolved" "https://registry.npmjs.org/@antv/matrix-util/-/matrix-util-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "@antv/util" "^2.0.9"
    "gl-matrix" "^3.3.0"
    "tslib" "^2.0.3"

"@antv/matrix-util@^3.1.0-beta.1", "@antv/matrix-util@^3.1.0-beta.2":
  "integrity" "sha512-W2R6Za3A6CmG51Y/4jZUM/tFgYSq7vTqJL1VD9dKrvwxS4sE0ZcXINtkp55CdyBwJ6Cwm8pfoRpnD4FnHahN0A=="
  "resolved" "https://registry.npmjs.org/@antv/matrix-util/-/matrix-util-3.1.0-beta.3.tgz"
  "version" "3.1.0-beta.3"
  dependencies:
    "@antv/util" "^2.0.9"
    "gl-matrix" "^3.4.3"
    "tslib" "^2.0.3"

"@antv/path-util@^2.0.3", "@antv/path-util@~2.0.5", "@antv/path-util@~2.0.7":
  "integrity" "sha512-R2VLZ5C8PLPtr3VciNyxtjKqJ0XlANzpFb5sE9GE61UQqSRuSVSzIakMxjEPrpqbgc+s+y8i+fmc89Snu7qbNw=="
  "resolved" "https://registry.npmjs.org/@antv/path-util/-/path-util-2.0.15.tgz"
  "version" "2.0.15"
  dependencies:
    "@antv/matrix-util" "^3.0.4"
    "@antv/util" "^2.0.9"
    "tslib" "^2.0.3"

"@antv/scale@^0.3.7", "@antv/scale@~0.3.1":
  "integrity" "sha512-G19Mh9VYPX/Z5PXKJrORIYCjrA+Fmko6ZB4HN6Y8K0T9FeKaSJq+idIe7AUJhIIXDCrFk4LzW+t908NsORqxPg=="
  "resolved" "https://registry.npmjs.org/@antv/scale/-/scale-0.3.14.tgz"
  "version" "0.3.14"
  dependencies:
    "@antv/util" "~2.0.3"
    "fecha" "~4.2.0"
    "tslib" "^2.0.0"

"@antv/util@*", "@antv/util@^2.0.0", "@antv/util@^2.0.7", "@antv/util@^2.0.9", "@antv/util@~2.0.0", "@antv/util@~2.0.12", "@antv/util@~2.0.3", "@antv/util@~2.0.5":
  "integrity" "sha512-o6I9hi5CIUvLGDhth0RxNSFDRwXeywmt6ExR4+RmVAzIi48ps6HUy+svxOCayvrPBN37uE6TAc2KDofRo0nK9Q=="
  "resolved" "https://registry.npmjs.org/@antv/util/-/util-2.0.17.tgz"
  "version" "2.0.17"
  dependencies:
    "csstype" "^3.0.8"
    "tslib" "^2.0.3"

"@arco-design/color@^0.4.0":
  "integrity" "sha512-s7p9MSwJgHeL8DwcATaXvWT3m2SigKpxx4JA1BGPHL4gfvaQsmQfrLBDpjOJFJuJ2jG2dMt3R3P8Pm9E65q18g=="
  "resolved" "https://registry.npmjs.org/@arco-design/color/-/color-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "color" "^3.1.3"

"@arco-design/web-react@^2.25.1", "@arco-design/web-react@^2.32.2":
  "integrity" "sha512-IzSHoKZ+2hYwf7gGYaISrw4RMzEzw9DoOeA8fggxikrwKs8BrzwwfpxTpJ4MGekerhbFDmereCrYNJnVnNrigA=="
  "resolved" "https://registry.npmmirror.com/@arco-design/web-react/-/web-react-2.65.0.tgz"
  "version" "2.65.0"
  dependencies:
    "@arco-design/color" "^0.4.0"
    "@babel/runtime" "^7.5.5"
    "b-tween" "^0.3.3"
    "b-validate" "^1.4.2"
    "compute-scroll-into-view" "^1.0.17"
    "dayjs" "^1.10.5"
    "lodash" "^4.17.21"
    "number-precision" "^1.3.1"
    "react-focus-lock" "^2.13.2"
    "react-is" "^18.2.0"
    "react-transition-group" "^4.3.0"
    "resize-observer-polyfill" "^1.5.1"
    "scroll-into-view-if-needed" "^2.2.20"
    "shallowequal" "^1.1.0"

"@arco-design/webpack-plugin@^1.6.0":
  "integrity" "sha512-dRDXaNK9pzjTfxo6jQe2oZs9PLJIF6kovT5HJCM843XgdClSyvVvlZo+xtzK84Y+VRL+YE4zUt3Jb+3AEp5gqA=="
  "resolved" "https://registry.npmjs.org/@arco-design/webpack-plugin/-/webpack-plugin-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "babel-plugin-import" "^1.0.0"
    "chalk" "^4.1.0"
    "lodash" "^4.17.20"
    "micromatch" "^4.0.2"

"@arco-plugins/vite-plugin-svgr@^0.7.2":
  "integrity" "sha512-oDhLSB6Q++NQWNCSRqSC4rGQkqPejtBqmgqdyLoD3lcq5dsf1qkY15vXSd4TB56yptSTPdUIZx/sE6130mZf8w=="
  "resolved" "https://registry.npmmirror.com/@arco-plugins/vite-plugin-svgr/-/vite-plugin-svgr-0.7.2.tgz"
  "version" "0.7.2"
  dependencies:
    "@svgr/core" "^6.0.0"

"@arco-plugins/vite-react@^1.3.3":
  "integrity" "sha512-fcGh/69xKGqIjMdIw6NCqst4uWME/WTYjiC6AxHbKBmeYSONazBYd+KtEDx9Be2N0pF4+sk+MGUDy3mrF13EdA=="
  "resolved" "https://registry.npmmirror.com/@arco-plugins/vite-react/-/vite-react-1.3.3.tgz"
  "version" "1.3.3"
  dependencies:
    "@babel/generator" "^7.12.11"
    "@babel/helper-module-imports" "^7.12.5"
    "@babel/parser" "^7.12.11"
    "@babel/traverse" "^7.12.12"
    "@babel/types" "^7.12.12"
    "@types/node" "^16.11.10"

"@arco-themes/react-arco-pro@^0.0.7":
  "integrity" "sha512-ymLuKbfwdYha9noATRQXe5qQH4THjqlEkZTWtAysq4GssYeemNObof51NnuJSMyQtdTS8KC7r//+zHjZrk4dcA=="
  "resolved" "https://registry.npmjs.org/@arco-themes/react-arco-pro/-/react-arco-pro-0.0.7.tgz"
  "version" "0.0.7"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.27.1":
  "integrity" "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg=="
  "resolved" "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    "js-tokens" "^4.0.0"
    "picocolors" "^1.1.1"

"@babel/compat-data@^7.13.11", "@babel/compat-data@^7.16.0", "@babel/compat-data@^7.16.4", "@babel/compat-data@^7.27.2":
  "integrity" "sha512-TUtMJYRPyUb/9aU8f3K0mjmjf6M9N5Woshn2CS6nqJSeJtTtQcpLUXjGt9vbF8ZGff0El99sWkLgzwW3VXnxZQ=="
  "resolved" "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.27.2.tgz"
  "version" "7.27.2"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.12.0", "@babel/core@^7.12.3", "@babel/core@^7.13.0", "@babel/core@^7.15.5", "@babel/core@^7.17.10", "@babel/core@^7.4.0-0":
  "integrity" "sha512-IaaGWsQqfsQWVLqMn9OB92MNN7zukfVA4s7KKAI0KfrrDsZ0yhi5uV4baBuLuN7n3vsZpwP8asPPcVwApxvjBQ=="
  "resolved" "https://registry.npmmirror.com/@babel/core/-/core-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.1"
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helpers" "^7.27.1"
    "@babel/parser" "^7.27.1"
    "@babel/template" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"
    "convert-source-map" "^2.0.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.2.3"
    "semver" "^6.3.1"

"@babel/generator@^7.12.11", "@babel/generator@^7.27.1":
  "integrity" "sha512-UnJfnIpc/+JO0/+KRVQNGU+y5taA5vCbwN8+azkX6beii/ZF+enZJSOKo11ZSzGJjlNfJHfQtmQT8H+9TXPG2w=="
  "resolved" "https://registry.npmmirror.com/@babel/generator/-/generator-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/parser" "^7.27.1"
    "@babel/types" "^7.27.1"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    "jsesc" "^3.0.2"

"@babel/helper-annotate-as-pure@^7.16.0", "@babel/helper-annotate-as-pure@^7.27.1":
  "integrity" "sha512-WnuuDILl9oOBbKnb4L+DyODx7iC47XfzmNCpTttFsSp6hTG7XZxu60+4IO+2/hPfcGOoKbFiwoI/+zwARbNQow=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.16.0":
  "integrity" "sha512-9KuleLT0e77wFUku6TUkqZzCEymBdtuQQ27MhEKzf9UOOJu3cYj98kyaDAzxpC7lV6DGiZFuC8XqDsq8/Kl6aQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.16.0"
    "@babel/types" "^7.16.0"

"@babel/helper-compilation-targets@^7.13.0", "@babel/helper-compilation-targets@^7.16.0", "@babel/helper-compilation-targets@^7.16.3", "@babel/helper-compilation-targets@^7.27.1":
  "integrity" "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz"
  "version" "7.27.2"
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    "browserslist" "^4.24.0"
    "lru-cache" "^5.1.1"
    "semver" "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.16.0":
  "integrity" "sha512-XLwWvqEaq19zFlF5PTgOod4bUA+XbkR4WLQBct1bkzmxJGB0ZEJaoKF4c8cgH9oBtCDuYJ8BP5NB9uFiEgO5QA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.0"
    "@babel/helper-function-name" "^7.16.0"
    "@babel/helper-member-expression-to-functions" "^7.16.0"
    "@babel/helper-optimise-call-expression" "^7.16.0"
    "@babel/helper-replace-supers" "^7.16.0"
    "@babel/helper-split-export-declaration" "^7.16.0"

"@babel/helper-create-regexp-features-plugin@^7.16.0":
  "integrity" "sha512-3DyG0zAFAZKcOp7aVr33ddwkxJ0Z0Jr5V99y3I690eYLpukJsJvAbzTy1ewoCqsML8SbIrjH14Jc/nSQ4TvNPA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.0"
    "regexpu-core" "^4.7.1"

"@babel/helper-define-polyfill-provider@^0.3.0":
  "integrity" "sha512-7hfT8lUljl/tM3h+izTX/pO3W3frz2ok6Pk+gzys8iJqDfZrZy2pXjRTZAvG2YmfHun1X4q8/UZRLatMfqc5Tg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "@babel/helper-compilation-targets" "^7.13.0"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/traverse" "^7.13.0"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"
    "semver" "^6.1.2"

"@babel/helper-explode-assignable-expression@^7.16.0":
  "integrity" "sha512-Hk2SLxC9ZbcOhLpg/yMznzJ11W++lg5GMbxt1ev6TXUiJB0N42KPC+7w8a+eWGuqDnUYuwStJoZHM7RgmIOaGQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-function-name@^7.16.0":
  "integrity" "sha512-QfDfEnIUyyBSR3HtrtGECuZ6DAyCkYFp7GHl75vFtTnn6pjKeK0T1DB5lLkFvBea8MdaiUABx3osbgLyInoejA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/helper-get-function-arity" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-get-function-arity@^7.16.7":
  "integrity" "sha512-flc+RLSOBXzNzVhcLu6ujeHUrD6tANAOU5ojrRx/as+tbzf8+stUCj7+IfRRoAbEZqj/ahXEMsjhOhgeZsrnTw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-get-function-arity/-/helper-get-function-arity-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-hoist-variables@^7.16.0":
  "integrity" "sha512-m04d/0Op34H5v7pbZw6pSKP7weA6lsMvfiIAMeIvkY/R4xQtBSMFEigu9QTZ2qB/9l22vsxtM8a+Q8CzD255fg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-member-expression-to-functions@^7.16.0":
  "integrity" "sha512-bsjlBFPuWT6IWhl28EdrQ+gTvSvj5tqVP5Xeftp07SEuz5pLnsXZuDkDD3Rfcxy0IsHmbZ+7B2/9SHzxO0T+sQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.12.5", "@babel/helper-module-imports@^7.16.0", "@babel/helper-module-imports@^7.27.1":
  "integrity" "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.16.0", "@babel/helper-module-transforms@^7.27.1":
  "integrity" "sha512-9yHn519/8KvTU5BjTVEEeIM3w9/2yXNKoD82JifINImhpKkARMJKPP59kLo+BafpdN5zgNeIcS4jsGDmd3l58g=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-optimise-call-expression@^7.16.0":
  "integrity" "sha512-SuI467Gi2V8fkofm2JPnZzB/SUuXoJA5zXe/xzyPP2M04686RzFKFHPK6HDVN6JvWBIEW8tt9hPR7fXdn2Lgpw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.13.0", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.27.1", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  "integrity" "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-remap-async-to-generator@^7.16.0", "@babel/helper-remap-async-to-generator@^7.16.4":
  "integrity" "sha512-vGERmmhR+s7eH5Y/cp8PCVzj4XEjerq8jooMfxFdA5xVtAk9Sh4AQsrWgiErUEBjtGrBtOFKDUcWQFW4/dFwMA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.16.4.tgz"
  "version" "7.16.4"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.0"
    "@babel/helper-wrap-function" "^7.16.0"
    "@babel/types" "^7.16.0"

"@babel/helper-replace-supers@^7.16.0":
  "integrity" "sha512-TQxuQfSCdoha7cpRNJvfaYxxxzmbxXw/+6cS7V02eeDYyhxderSoMVALvwupA54/pZcOTtVeJ0xccp1nGWladA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.16.0"
    "@babel/helper-optimise-call-expression" "^7.16.0"
    "@babel/traverse" "^7.16.0"
    "@babel/types" "^7.16.0"

"@babel/helper-simple-access@^7.16.0":
  "integrity" "sha512-o1rjBT/gppAqKsYfUdfHq5Rk03lMQrkPHG1OWzHWpLgVXRH4HnMM9Et9CVdIqwkCQlobnGHEJMsgWP/jE1zUiw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-skip-transparent-expression-wrappers@^7.16.0":
  "integrity" "sha512-+il1gTy0oHwUsBQZyJvukbB4vPMdcYBrFHa0Uc4AizLxbq6BOYC51Rv4tWocX9BLBDLZ4kc6qUFpQ6HRgL+3zw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-split-export-declaration@^7.16.0":
  "integrity" "sha512-xbWoy/PFoxSWazIToT9Sif+jJTlrMcndIsaOKvTA6u7QEo7ilkRZpjew18/W3c7nm8fXdUDXh02VXTbZ0pGDNw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.16.7.tgz"
  "version" "7.16.7"
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-string-parser@^7.27.1":
  "integrity" "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-validator-identifier@^7.15.7", "@babel/helper-validator-identifier@^7.27.1":
  "integrity" "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-validator-option@^7.14.5", "@babel/helper-validator-option@^7.27.1":
  "integrity" "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-wrap-function@^7.16.0":
  "integrity" "sha512-VVMGzYY3vkWgCJML+qVLvGIam902mJW0FvT7Avj1zEe0Gn7D93aWdLblYARTxEw+6DhZmtzhBM2zv0ekE5zg1g=="
  "resolved" "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-function-name" "^7.16.0"
    "@babel/template" "^7.16.0"
    "@babel/traverse" "^7.16.0"
    "@babel/types" "^7.16.0"

"@babel/helpers@^7.27.1":
  "integrity" "sha512-FCvFTm0sWV8Fxhpp2McP5/W53GPllQ9QeQ7SiqGWjMf/LVG07lFa5+pgK05IRhVwtvafT22KF+ZSnM9I545CvQ=="
  "resolved" "https://registry.npmmirror.com/@babel/helpers/-/helpers-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/template" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/parser@^7.12.11", "@babel/parser@^7.27.1", "@babel/parser@^7.27.2":
  "integrity" "sha512-QYLs8299NA7WM/bZAdp+CviYYkVoYXlDW2rzliy3chxd1PQjej7JORuMJDJXJUb9g0TT+B99EwaVLKmX+sPXWw=="
  "resolved" "https://registry.npmmirror.com/@babel/parser/-/parser-7.27.2.tgz"
  "version" "7.27.2"
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.16.2":
  "integrity" "sha512-h37CvpLSf8gb2lIJ2CgC3t+EjFbi0t8qS7LCS1xcJIlEXE4czlofwaW7W1HA8zpgOCzI9C1nmoqNR1zWkk0pQg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.16.2.tgz"
  "version" "7.16.2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.16.0":
  "integrity" "sha512-4tcFwwicpWTrpl9qjf7UsoosaArgImF85AxqCRZlgc3IQDvkUHjJpruXAL58Wmj+T6fypWTC/BakfEkwIL/pwA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.0"

"@babel/plugin-proposal-async-generator-functions@^7.16.4":
  "integrity" "sha512-/CUekqaAaZCQHleSK/9HajvcD/zdnJiKRiuUFq8ITE+0HsPzquf53cpFiqAwl/UfmJbR6n5uGPQSPdrmKOvHHg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.16.4.tgz"
  "version" "7.16.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-remap-async-to-generator" "^7.16.4"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.16.0":
  "integrity" "sha512-mCF3HcuZSY9Fcx56Lbn+CGdT44ioBMMvjNVldpKtj8tpniETdLjnxdHI1+sDWXIM1nNt+EanJOZ3IG9lzVjs7A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-proposal-class-static-block@^7.16.0":
  "integrity" "sha512-mAy3sdcY9sKAkf3lQbDiv3olOfiLqI51c9DR9b19uMoR2Z6r5pmGl7dfNFqEvqOyqbf1ta4lknK4gc5PJn3mfA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-dynamic-import@^7.16.0":
  "integrity" "sha512-QGSA6ExWk95jFQgwz5GQ2Dr95cf7eI7TKutIXXTb7B1gCLTCz5hTjFTQGfLFBBiC5WSNi7udNwWsqbbMh1c4yQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.16.0":
  "integrity" "sha512-CjI4nxM/D+5wCnhD11MHB1AwRSAYeDT+h8gCdcVJZ/OK7+wRzFsf7PFPWVpVpNRkHMmMkQWAHpTq+15IXQ1diA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.16.0":
  "integrity" "sha512-kouIPuiv8mSi5JkEhzApg5Gn6hFyKPnlkO0a9YSzqRurH8wYzSlf6RJdzluAsbqecdW5pBvDJDfyDIUR/vLxvg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.16.0":
  "integrity" "sha512-pbW0fE30sVTYXXm9lpVQQ/Vc+iTeQKiXlaNRZPPN2A2VdlWyAtsUrsQ3xydSlDW00TFMK7a8m3cDTkBF5WnV3Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.16.0":
  "integrity" "sha512-3bnHA8CAFm7cG93v8loghDYyQ8r97Qydf63BeYiGgYbjKKB/XP53W15wfRC7dvKfoiJ34f6Rbyyx2btExc8XsQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.16.0":
  "integrity" "sha512-FAhE2I6mjispy+vwwd6xWPyEx3NYFS13pikDBWUAFGZvq6POGs5eNchw8+1CYoEgBl9n11I3NkzD7ghn25PQ9Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.16.0":
  "integrity" "sha512-LU/+jp89efe5HuWJLmMmFG0+xbz+I2rSI7iLc1AlaeSMDMOGzWlc5yJrMN1d04osXN4sSfpo4O+azkBNBes0jg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/compat-data" "^7.16.0"
    "@babel/helper-compilation-targets" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.16.0"

"@babel/plugin-proposal-optional-catch-binding@^7.16.0":
  "integrity" "sha512-kicDo0A/5J0nrsCPbn89mTG3Bm4XgYi0CZtvex9Oyw7gGZE3HXGD0zpQNH+mo+tEfbo8wbmMvJftOwpmPy7aVw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.16.0":
  "integrity" "sha512-Y4rFpkZODfHrVo70Uaj6cC1JJOt3Pp0MdWSwIKtb8z1/lsjl9AmnB7ErRFV+QNGIfcY1Eruc2UMx5KaRnXjMyg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.16.0":
  "integrity" "sha512-IvHmcTHDFztQGnn6aWq4t12QaBXTKr1whF/dgp9kz84X6GUcwq9utj7z2wFCUfeOup/QKnOlt2k0zxkGFx9ubg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-proposal-private-property-in-object@^7.16.0":
  "integrity" "sha512-3jQUr/HBbMVZmi72LpjQwlZ55i1queL8KcDTQEkAHihttJnAPrcvG9ZNXIfsd2ugpizZo595egYV6xy+pv4Ofw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.0"
    "@babel/helper-create-class-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.16.0", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  "integrity" "sha512-ti7IdM54NXv29cA4+bNNKEMS4jLMCbJgl+Drv+FgYy0erJLAxNAIXcNjNjrRZEcWq0xJHsNVwQezskMFpF8N9g=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  "integrity" "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  "integrity" "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  "integrity" "sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  "integrity" "sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.27.1":
  "integrity" "sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  "integrity" "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  "integrity" "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  "integrity" "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  "integrity" "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-arrow-functions@^7.16.0":
  "integrity" "sha512-vIFb5250Rbh7roWARvCLvIJ/PtAU5Lhv7BtZ1u24COwpI9Ypjsh+bZcKk6rlIyalK+r0jOc1XQ8I4ovNxNrWrA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-async-to-generator@^7.16.0":
  "integrity" "sha512-PbIr7G9kR8tdH6g8Wouir5uVjklETk91GMVSUq+VaOgiinbCkBP6Q7NN/suM/QutZkMJMvcyAriogcYAdhg8Gw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-module-imports" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-remap-async-to-generator" "^7.16.0"

"@babel/plugin-transform-block-scoped-functions@^7.16.0":
  "integrity" "sha512-V14As3haUOP4ZWrLJ3VVx5rCnrYhMSHN/jX7z6FAt5hjRkLsb0snPCmJwSOML5oxkKO4FNoNv7V5hw/y2bjuvg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-block-scoping@^7.16.0":
  "integrity" "sha512-27n3l67/R3UrXfizlvHGuTwsRIFyce3D/6a37GRxn28iyTPvNXaW4XvznexRh1zUNLPjbLL22Id0XQElV94ruw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-classes@^7.16.0":
  "integrity" "sha512-HUxMvy6GtAdd+GKBNYDWCIA776byUQH8zjnfjxwT1P1ARv/wFu8eBDpmXQcLS/IwRtrxIReGiplOwMeyO7nsDQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.0"
    "@babel/helper-function-name" "^7.16.0"
    "@babel/helper-optimise-call-expression" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-replace-supers" "^7.16.0"
    "@babel/helper-split-export-declaration" "^7.16.0"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.16.0":
  "integrity" "sha512-63l1dRXday6S8V3WFY5mXJwcRAnPYxvFfTlt67bwV1rTyVTM5zrp0DBBb13Kl7+ehkCVwIZPumPpFP/4u70+Tw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-destructuring@^7.16.0":
  "integrity" "sha512-Q7tBUwjxLTsHEoqktemHBMtb3NYwyJPTJdM+wDwb0g8PZ3kQUIzNvwD5lPaqW/p54TXBc/MXZu9Jr7tbUEUM8Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-dotall-regex@^7.16.0", "@babel/plugin-transform-dotall-regex@^7.4.4":
  "integrity" "sha512-FXlDZfQeLILfJlC6I1qyEwcHK5UpRCFkaoVyA1nk9A1L1Yu583YO4un2KsLBsu3IJb4CUbctZks8tD9xPQubLw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-duplicate-keys@^7.16.0":
  "integrity" "sha512-LIe2kcHKAZOJDNxujvmp6z3mfN6V9lJxubU4fJIGoQCkKe3Ec2OcbdlYP+vW++4MpxwG0d1wSDOJtQW5kLnkZQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-exponentiation-operator@^7.16.0":
  "integrity" "sha512-OwYEvzFI38hXklsrbNivzpO3fh87skzx8Pnqi4LoSYeav0xHlueSoCJrSgTPfnbyzopo5b3YVAJkFIcUpK2wsw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-for-of@^7.16.0":
  "integrity" "sha512-5QKUw2kO+GVmKr2wMYSATCTTnHyscl6sxFRAY+rvN7h7WB0lcG0o4NoV6ZQU32OZGVsYUsfLGgPQpDFdkfjlJQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-function-name@^7.16.0":
  "integrity" "sha512-lBzMle9jcOXtSOXUpc7tvvTpENu/NuekNJVova5lCCWCV9/U1ho2HH2y0p6mBg8fPm/syEAbfaaemYGOHCY3mg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-function-name" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-literals@^7.16.0":
  "integrity" "sha512-gQDlsSF1iv9RU04clgXqRjrPyyoJMTclFt3K1cjLmTKikc0s/6vE3hlDeEVC71wLTRu72Fq7650kABrdTc2wMQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-member-expression-literals@^7.16.0":
  "integrity" "sha512-WRpw5HL4Jhnxw8QARzRvwojp9MIE7Tdk3ez6vRyUk1MwgjJN0aNpRoXainLR5SgxmoXx/vsXGZ6OthP6t/RbUg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-modules-amd@^7.16.0":
  "integrity" "sha512-rWFhWbCJ9Wdmzln1NmSCqn7P0RAD+ogXG/bd9Kg5c7PKWkJtkiXmYsMBeXjDlzHpVTJ4I/hnjs45zX4dEv81xw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-module-transforms" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.12.1", "@babel/plugin-transform-modules-commonjs@^7.16.0":
  "integrity" "sha512-Dzi+NWqyEotgzk/sb7kgQPJQf7AJkQBWsVp1N6JWc1lBVo0vkElUnGdr1PzUBmfsCCN5OOFya3RtpeHk15oLKQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-module-transforms" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-simple-access" "^7.16.0"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.16.0":
  "integrity" "sha512-yuGBaHS3lF1m/5R+6fjIke64ii5luRUg97N2wr+z1sF0V+sNSXPxXDdEEL/iYLszsN5VKxVB1IPfEqhzVpiqvg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-hoist-variables" "^7.16.0"
    "@babel/helper-module-transforms" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-identifier" "^7.15.7"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.16.0":
  "integrity" "sha512-nx4f6no57himWiHhxDM5pjwhae5vLpTK2zCnDH8+wNLJy0TVER/LJRHl2bkt6w9Aad2sPD5iNNoUpY3X9sTGDg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-module-transforms" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-named-capturing-groups-regex@^7.16.0":
  "integrity" "sha512-LogN88uO+7EhxWc8WZuQ8vxdSyVGxhkh8WTC3tzlT8LccMuQdA81e9SGV6zY7kY2LjDhhDOFdQVxdGwPyBCnvg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.0"

"@babel/plugin-transform-new-target@^7.16.0":
  "integrity" "sha512-fhjrDEYv2DBsGN/P6rlqakwRwIp7rBGLPbrKxwh7oVt5NNkIhZVOY2GRV+ULLsQri1bDqwDWnU3vhlmx5B2aCw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-object-super@^7.16.0":
  "integrity" "sha512-fds+puedQHn4cPLshoHcR1DTMN0q1V9ou0mUjm8whx9pGcNvDrVVrgw+KJzzCaiTdaYhldtrUps8DWVMgrSEyg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-replace-supers" "^7.16.0"

"@babel/plugin-transform-parameters@^7.16.0", "@babel/plugin-transform-parameters@^7.16.3":
  "integrity" "sha512-3MaDpJrOXT1MZ/WCmkOFo7EtmVVC8H4EUZVrHvFOsmwkk4lOjQj8rzv8JKUZV4YoQKeoIgk07GO+acPU9IMu/w=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.16.3.tgz"
  "version" "7.16.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-property-literals@^7.16.0":
  "integrity" "sha512-XLldD4V8+pOqX2hwfWhgwXzGdnDOThxaNTgqagOcpBgIxbUvpgU2FMvo5E1RyHbk756WYgdbS0T8y0Cj9FKkWQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-react-constant-elements@^7.12.1":
  "integrity" "sha512-OgtklS+p9t1X37eWA4XdvvbZG/3gqzX569gqmo3q4/Ui6qjfTQmOs5UTSrfdD9nVByHhX6Gbm/Pyc4KbwUXGWA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-constant-elements/-/plugin-transform-react-constant-elements-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-react-display-name@^7.16.0":
  "integrity" "sha512-FJFdJAqaCpndL+pIf0aeD/qlQwT7QXOvR6Cc8JPvNhKJBi2zc/DPc4g05Y3fbD/0iWAMQFGij4+Xw+4L/BMpTg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-react-jsx-development@^7.16.0", "@babel/plugin-transform-react-jsx-development@^7.16.7":
  "integrity" "sha512-ykDdF5yI4f1WrAolLqeF3hmYU12j9ntLQl/AOG1HAS21jxyg1Q0/J/tpREuYLfatGdGmXp/3yS0ZA76kOlVq9Q=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.27.1"

"@babel/plugin-transform-react-jsx-self@^7.16.7":
  "integrity" "sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-source@^7.16.7":
  "integrity" "sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx@^7.16.0", "@babel/plugin-transform-react-jsx@^7.17.3", "@babel/plugin-transform-react-jsx@^7.27.1":
  "integrity" "sha512-2KH4LWGSrJIkVf5tSiBFYuXDAoWRq2MMwgivCf+93dd0GQi8RXLjKA/0EvRnVV5G0hrHczsquXuD01L8s6dmBw=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-syntax-jsx" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/plugin-transform-react-pure-annotations@^7.16.0":
  "integrity" "sha512-NC/Bj2MG+t8Ef5Pdpo34Ay74X4Rt804h5y81PwOpfPtmAK3i6CizmQqwyBQzIepz1Yt8wNr2Z2L7Lu3qBMfZMA=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-regenerator@^7.16.0":
  "integrity" "sha512-JAvGxgKuwS2PihiSFaDrp94XOzzTUeDeOQlcKzVAyaPap7BnZXK/lvMDiubkPTdotPKOIZq9xWXWnggUMYiExg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "regenerator-transform" "^0.14.2"

"@babel/plugin-transform-reserved-words@^7.16.0":
  "integrity" "sha512-Dgs8NNCehHSvXdhEhln8u/TtJxfVwGYCgP2OOr5Z3Ar+B+zXicEOKNTyc+eca2cuEOMtjW6m9P9ijOt8QdqWkg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-runtime@^7.12.10":
  "integrity" "sha512-pru6+yHANMTukMtEZGC4fs7XPwg35v8sj5CIEmE+gEkFljFiVJxEWxx/7ZDkTK+iZRYo1bFXBtfIN95+K3cJ5A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.16.4.tgz"
  "version" "7.16.4"
  dependencies:
    "@babel/helper-module-imports" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "babel-plugin-polyfill-corejs2" "^0.3.0"
    "babel-plugin-polyfill-corejs3" "^0.4.0"
    "babel-plugin-polyfill-regenerator" "^0.3.0"
    "semver" "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.16.0":
  "integrity" "sha512-iVb1mTcD8fuhSv3k99+5tlXu5N0v8/DPm2mO3WACLG6al1CGZH7v09HJyUb1TtYl/Z+KrM6pHSIJdZxP5A+xow=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-spread@^7.16.0":
  "integrity" "sha512-Ao4MSYRaLAQczZVp9/7E7QHsCuK92yHRrmVNRe/SlEJjhzivq0BSn8mEraimL8wizHZ3fuaHxKH0iwzI13GyGg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"

"@babel/plugin-transform-sticky-regex@^7.16.0":
  "integrity" "sha512-/ntT2NljR9foobKk4E/YyOSwcGUXtYWv5tinMK/3RkypyNBNdhHUaq6Orw5DWq9ZcNlS03BIlEALFeQgeVAo4Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-template-literals@^7.16.0":
  "integrity" "sha512-Rd4Ic89hA/f7xUSJQk5PnC+4so50vBoBfxjdQAdvngwidM8jYIBVxBZ/sARxD4e0yMXRbJVDrYf7dyRtIIKT6Q=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-typeof-symbol@^7.16.0":
  "integrity" "sha512-++V2L8Bdf4vcaHi2raILnptTBjGEFxn5315YU+e8+EqXIucA+q349qWngCLpUYqqv233suJ6NOienIVUpS9cqg=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-unicode-escapes@^7.16.0":
  "integrity" "sha512-VFi4dhgJM7Bpk8lRc5CMaRGlKZ29W9C3geZjt9beuzSUrlJxsNwX7ReLwaL6WEvsOf2EQkyIJEPtF8EXjB/g2A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-unicode-regex@^7.16.0":
  "integrity" "sha512-jHLK4LxhHjvCeZDWyA9c+P9XH1sOxRd1RO9xMtDVRAOND/PczPqizEtVdx4TQF/wyPaewqpT+tgQFYMnN/P94A=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/preset-env@^7.12.1":
  "integrity" "sha512-v0QtNd81v/xKj4gNKeuAerQ/azeNn/G1B1qMLeXOcV8+4TWlD2j3NV1u8q29SDFBXx/NBq5kyEAO+0mpRgacjA=="
  "resolved" "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.16.4.tgz"
  "version" "7.16.4"
  dependencies:
    "@babel/compat-data" "^7.16.4"
    "@babel/helper-compilation-targets" "^7.16.3"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-option" "^7.14.5"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.16.2"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.16.0"
    "@babel/plugin-proposal-async-generator-functions" "^7.16.4"
    "@babel/plugin-proposal-class-properties" "^7.16.0"
    "@babel/plugin-proposal-class-static-block" "^7.16.0"
    "@babel/plugin-proposal-dynamic-import" "^7.16.0"
    "@babel/plugin-proposal-export-namespace-from" "^7.16.0"
    "@babel/plugin-proposal-json-strings" "^7.16.0"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.16.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.16.0"
    "@babel/plugin-proposal-numeric-separator" "^7.16.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.16.0"
    "@babel/plugin-proposal-optional-catch-binding" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.0"
    "@babel/plugin-proposal-private-methods" "^7.16.0"
    "@babel/plugin-proposal-private-property-in-object" "^7.16.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.16.0"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.16.0"
    "@babel/plugin-transform-async-to-generator" "^7.16.0"
    "@babel/plugin-transform-block-scoped-functions" "^7.16.0"
    "@babel/plugin-transform-block-scoping" "^7.16.0"
    "@babel/plugin-transform-classes" "^7.16.0"
    "@babel/plugin-transform-computed-properties" "^7.16.0"
    "@babel/plugin-transform-destructuring" "^7.16.0"
    "@babel/plugin-transform-dotall-regex" "^7.16.0"
    "@babel/plugin-transform-duplicate-keys" "^7.16.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.16.0"
    "@babel/plugin-transform-for-of" "^7.16.0"
    "@babel/plugin-transform-function-name" "^7.16.0"
    "@babel/plugin-transform-literals" "^7.16.0"
    "@babel/plugin-transform-member-expression-literals" "^7.16.0"
    "@babel/plugin-transform-modules-amd" "^7.16.0"
    "@babel/plugin-transform-modules-commonjs" "^7.16.0"
    "@babel/plugin-transform-modules-systemjs" "^7.16.0"
    "@babel/plugin-transform-modules-umd" "^7.16.0"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.16.0"
    "@babel/plugin-transform-new-target" "^7.16.0"
    "@babel/plugin-transform-object-super" "^7.16.0"
    "@babel/plugin-transform-parameters" "^7.16.3"
    "@babel/plugin-transform-property-literals" "^7.16.0"
    "@babel/plugin-transform-regenerator" "^7.16.0"
    "@babel/plugin-transform-reserved-words" "^7.16.0"
    "@babel/plugin-transform-shorthand-properties" "^7.16.0"
    "@babel/plugin-transform-spread" "^7.16.0"
    "@babel/plugin-transform-sticky-regex" "^7.16.0"
    "@babel/plugin-transform-template-literals" "^7.16.0"
    "@babel/plugin-transform-typeof-symbol" "^7.16.0"
    "@babel/plugin-transform-unicode-escapes" "^7.16.0"
    "@babel/plugin-transform-unicode-regex" "^7.16.0"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.16.0"
    "babel-plugin-polyfill-corejs2" "^0.3.0"
    "babel-plugin-polyfill-corejs3" "^0.4.0"
    "babel-plugin-polyfill-regenerator" "^0.3.0"
    "core-js-compat" "^3.19.1"
    "semver" "^6.3.0"

"@babel/preset-modules@^0.1.5":
  "integrity" "sha512-A57th6YRG7oR3cq/yt/Y84MvGgE0eJG2F1JLhKuyG+jFxEgrd/HAMJatiFtmOiZurz+0DkrvbheCLaV5f2JfjA=="
  "resolved" "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    "esutils" "^2.0.2"

"@babel/preset-react@^7.12.5":
  "integrity" "sha512-d31IFW2bLRB28uL1WoElyro8RH5l6531XfxMtCeCmp6RVAF1uTfxxUA0LH1tXl+psZdwfmIbwoG4U5VwgbhtLw=="
  "resolved" "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-option" "^7.14.5"
    "@babel/plugin-transform-react-display-name" "^7.16.0"
    "@babel/plugin-transform-react-jsx" "^7.16.0"
    "@babel/plugin-transform-react-jsx-development" "^7.16.0"
    "@babel/plugin-transform-react-pure-annotations" "^7.16.0"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.1.2", "@babel/runtime@^7.11.2", "@babel/runtime@^7.12.1", "@babel/runtime@^7.12.13", "@babel/runtime@^7.15.4", "@babel/runtime@^7.5.5", "@babel/runtime@^7.7.7", "@babel/runtime@^7.8.4", "@babel/runtime@^7.8.7", "@babel/runtime@^7.9.2":
  "integrity" "sha512-WBwekcqacdY2e9AF/Q7WLFUWmdJGJTkbjqTjoMDgXkVZ3ZRUvOPsLb5KdwISoQVsbP+DQzVZW4Zhci0DvpbNTQ=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.16.3.tgz"
  "version" "7.16.3"
  dependencies:
    "regenerator-runtime" "^0.13.4"

"@babel/template@^7.16.0", "@babel/template@^7.16.7", "@babel/template@^7.27.1":
  "integrity" "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw=="
  "resolved" "https://registry.npmmirror.com/@babel/template/-/template-7.27.2.tgz"
  "version" "7.27.2"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.12.12", "@babel/traverse@^7.13.0", "@babel/traverse@^7.16.0", "@babel/traverse@^7.27.1":
  "integrity" "sha512-ZCYtZciz1IWJB4U61UPu4KEaqyfj+r5T1Q5mqPo+IBpcG9kHv30Z0aD8LXPgC1trYa6rK0orRyAhqUgk4MjmEg=="
  "resolved" "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.1"
    "@babel/parser" "^7.27.1"
    "@babel/template" "^7.27.1"
    "@babel/types" "^7.27.1"
    "debug" "^4.3.1"
    "globals" "^11.1.0"

"@babel/types@^7.12.12", "@babel/types@^7.12.6", "@babel/types@^7.15.6", "@babel/types@^7.16.0", "@babel/types@^7.16.7", "@babel/types@^7.27.1", "@babel/types@^7.4.4":
  "integrity" "sha512-+EzkxvLNfiUeKMgy/3luqfsCWFRXLb7U6wNQTk60tovuckwB15B191tJWvpp4HjiQWdJkCxO3Wbvc6jlk3Xb2Q=="
  "resolved" "https://registry.npmmirror.com/@babel/types/-/types-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@eslint/eslintrc@^1.2.0":
  "integrity" "sha512-igm9SjJHNEJRiUnecP/1R5T3wKLEJ7pL6e2P+GUSfCd0dGjPYYZve08uzw8L2J8foVHFz+NGu12JxRcU2gGo6w=="
  "resolved" "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.3.2"
    "espree" "^9.3.1"
    "globals" "^13.9.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.2.1"
    "js-yaml" "^4.1.0"
    "minimatch" "^3.0.4"
    "strip-json-comments" "^3.1.1"

"@humanwhocodes/config-array@^0.9.2":
  "integrity" "sha512-ObyMyWxZiCu/yTisA7uzx81s40xR2fD5Cg/2Kq7G02ajkNubJf6BopgDTmDyc3U7sXpNKM8cYOw7s7Tyr+DnCw=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.9.5.tgz"
  "version" "0.9.5"
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.1"
    "debug" "^4.1.1"
    "minimatch" "^3.0.4"

"@humanwhocodes/object-schema@^1.2.1":
  "integrity" "sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz"
  "version" "1.2.1"

"@icons/material@^0.2.4":
  "integrity" "sha512-QPcGmICAPbGLGb6F/yNf/KzKqvFx8z5qx3D1yFqVAjoFmXK35EgyW+cJ57Te3CNsmzblwtzakLGFqHPqrfb4Tw=="
  "resolved" "https://registry.npmjs.org/@icons/material/-/material-0.2.4.tgz"
  "version" "0.2.4"

"@jridgewell/gen-mapping@^0.3.5":
  "integrity" "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA=="
  "resolved" "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz"
  "version" "0.3.8"
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  "integrity" "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  "version" "3.1.2"

"@jridgewell/set-array@^1.2.1":
  "integrity" "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A=="
  "resolved" "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  "version" "1.2.1"

"@jridgewell/source-map@^0.3.3":
  "integrity" "sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
  "integrity" "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  "version" "1.5.0"

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  "integrity" "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  "version" "0.3.25"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@juggle/resize-observer@^3.3.1":
  "integrity" "sha512-zMM9Ds+SawiUkakS7y94Ymqx+S0ORzpG3frZirN3l+UlXUmSUR7hF4wxCVqW+ei94JzV5kt0uXBcoOEAuiydrw=="
  "resolved" "https://registry.npmjs.org/@juggle/resize-observer/-/resize-observer-3.3.1.tgz"
  "version" "3.3.1"

"@loadable/component@^5.13.2":
  "integrity" "sha512-g63rQzypPOZi0BeGsK4ST2MYhsFR+i7bhL8k/McUoWDNMDuTTdUlQ2GACKxqh5sI/dNC/6nVoPrycMnSylnAgQ=="
  "resolved" "https://registry.npmjs.org/@loadable/component/-/component-5.15.0.tgz"
  "version" "5.15.0"
  dependencies:
    "@babel/runtime" "^7.7.7"
    "hoist-non-react-statics" "^3.3.1"
    "react-is" "^16.12.0"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  "integrity" "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  "integrity" "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@rollup/pluginutils@^4.2.1":
  "integrity" "sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ=="
  "resolved" "https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "estree-walker" "^2.0.1"
    "picomatch" "^2.2.2"

"@svgr/babel-plugin-add-jsx-attribute@^5.4.0":
  "integrity" "sha512-ZFf2gs/8/6B8PnSofI0inYXr2SDNTDScPXhN7k5EqD4aZ3gi6u+rbmZHVB8IM3wDyx8ntKACZbtXSm7oZGRqVg=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-add-jsx-attribute/-/babel-plugin-add-jsx-attribute-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-add-jsx-attribute@^6.0.0":
  "integrity" "sha512-MdPdhdWLtQsjd29Wa4pABdhWbaRMACdM1h31BY+c6FghTZqNGT7pEYdBoaGeKtdTOBC/XNFQaKVj+r/Ei2ryWA=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-add-jsx-attribute/-/babel-plugin-add-jsx-attribute-6.0.0.tgz"
  "version" "6.0.0"

"@svgr/babel-plugin-remove-jsx-attribute@^5.4.0":
  "integrity" "sha512-yaS4o2PgUtwLFGTKbsiAy6D0o3ugcUhWK0Z45umJ66EPWunAz9fuFw2gJuje6wqQvQWOTJvIahUwndOXb7QCPg=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-attribute/-/babel-plugin-remove-jsx-attribute-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-remove-jsx-attribute@^6.0.0":
  "integrity" "sha512-aVdtfx9jlaaxc3unA6l+M9YRnKIZjOhQPthLKqmTXC8UVkBLDRGwPKo+r8n3VZN8B34+yVajzPTZ+ptTSuZZCw=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-attribute/-/babel-plugin-remove-jsx-attribute-6.0.0.tgz"
  "version" "6.0.0"

"@svgr/babel-plugin-remove-jsx-empty-expression@^5.0.1":
  "integrity" "sha512-LA72+88A11ND/yFIMzyuLRSMJ+tRKeYKeQ+mR3DcAZ5I4h5CPWN9AHyUzJbWSYp/u2u0xhmgOe0+E41+GjEueA=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-empty-expression/-/babel-plugin-remove-jsx-empty-expression-5.0.1.tgz"
  "version" "5.0.1"

"@svgr/babel-plugin-remove-jsx-empty-expression@^6.0.0":
  "integrity" "sha512-Ccj42ApsePD451AZJJf1QzTD1B/BOU392URJTeXFxSK709i0KUsGtbwyiqsKu7vsYxpTM0IA5clAKDyf9RCZyA=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-empty-expression/-/babel-plugin-remove-jsx-empty-expression-6.0.0.tgz"
  "version" "6.0.0"

"@svgr/babel-plugin-replace-jsx-attribute-value@^5.0.1":
  "integrity" "sha512-PoiE6ZD2Eiy5mK+fjHqwGOS+IXX0wq/YDtNyIgOrc6ejFnxN4b13pRpiIPbtPwHEc+NT2KCjteAcq33/F1Y9KQ=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-replace-jsx-attribute-value/-/babel-plugin-replace-jsx-attribute-value-5.0.1.tgz"
  "version" "5.0.1"

"@svgr/babel-plugin-replace-jsx-attribute-value@^6.0.0":
  "integrity" "sha512-88V26WGyt1Sfd1emBYmBJRWMmgarrExpKNVmI9vVozha4kqs6FzQJ/Kp5+EYli1apgX44518/0+t9+NU36lThQ=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-replace-jsx-attribute-value/-/babel-plugin-replace-jsx-attribute-value-6.0.0.tgz"
  "version" "6.0.0"

"@svgr/babel-plugin-svg-dynamic-title@^5.4.0":
  "integrity" "sha512-zSOZH8PdZOpuG1ZVx/cLVePB2ibo3WPpqo7gFIjLV9a0QsuQAzJiwwqmuEdTaW2pegyBE17Uu15mOgOcgabQZg=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-svg-dynamic-title/-/babel-plugin-svg-dynamic-title-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-svg-dynamic-title@^6.0.0":
  "integrity" "sha512-F7YXNLfGze+xv0KMQxrl2vkNbI9kzT9oDK55/kUuymh1ACyXkMV+VZWX1zEhSTfEKh7VkHVZGmVtHg8eTZ6PRg=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-svg-dynamic-title/-/babel-plugin-svg-dynamic-title-6.0.0.tgz"
  "version" "6.0.0"

"@svgr/babel-plugin-svg-em-dimensions@^5.4.0":
  "integrity" "sha512-cPzDbDA5oT/sPXDCUYoVXEmm3VIoAWAPT6mSPTJNbQaBNUuEKVKyGH93oDY4e42PYHRW67N5alJx/eEol20abw=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-svg-em-dimensions/-/babel-plugin-svg-em-dimensions-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-svg-em-dimensions@^6.0.0":
  "integrity" "sha512-+rghFXxdIqJNLQK08kwPBD3Z22/0b2tEZ9lKiL/yTfuyj1wW8HUXu4bo/XkogATIYuXSghVQOOCwURXzHGKyZA=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-svg-em-dimensions/-/babel-plugin-svg-em-dimensions-6.0.0.tgz"
  "version" "6.0.0"

"@svgr/babel-plugin-transform-react-native-svg@^5.4.0":
  "integrity" "sha512-3eYP/SaopZ41GHwXma7Rmxcv9uRslRDTY1estspeB1w1ueZWd/tPlMfEOoccYpEMZU3jD4OU7YitnXcF5hLW2Q=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-transform-react-native-svg/-/babel-plugin-transform-react-native-svg-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-transform-react-native-svg@^6.0.0":
  "integrity" "sha512-VaphyHZ+xIKv5v0K0HCzyfAaLhPGJXSk2HkpYfXIOKb7DjLBv0soHDxNv6X0vr2titsxE7klb++u7iOf7TSrFQ=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-transform-react-native-svg/-/babel-plugin-transform-react-native-svg-6.0.0.tgz"
  "version" "6.0.0"

"@svgr/babel-plugin-transform-svg-component@^5.5.0":
  "integrity" "sha512-q4jSH1UUvbrsOtlo/tKcgSeiCHRSBdXoIoqX1pgcKK/aU3JD27wmMKwGtpB8qRYUYoyXvfGxUVKchLuR5pB3rQ=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-transform-svg-component/-/babel-plugin-transform-svg-component-5.5.0.tgz"
  "version" "5.5.0"

"@svgr/babel-plugin-transform-svg-component@^6.0.0":
  "integrity" "sha512-cYNs24WpSyd62iiZl8TOFehBx6gBA6W8tCY0N1kNqhgJOzcKTyGijqsf+oG+sUV5vZ9SqagdspIk0nz2DD+r6Q=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-plugin-transform-svg-component/-/babel-plugin-transform-svg-component-6.0.0.tgz"
  "version" "6.0.0"

"@svgr/babel-preset@^5.5.0":
  "integrity" "sha512-4FiXBjvQ+z2j7yASeGPEi8VD/5rrGQk4Xrq3EdJmoZgz/tpqChpo5hgXDvmEauwtvOc52q8ghhZK4Oy7qph4ig=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-preset/-/babel-preset-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression" "^5.0.1"
    "@svgr/babel-plugin-replace-jsx-attribute-value" "^5.0.1"
    "@svgr/babel-plugin-svg-dynamic-title" "^5.4.0"
    "@svgr/babel-plugin-svg-em-dimensions" "^5.4.0"
    "@svgr/babel-plugin-transform-react-native-svg" "^5.4.0"
    "@svgr/babel-plugin-transform-svg-component" "^5.5.0"

"@svgr/babel-preset@^6.0.0":
  "integrity" "sha512-jB608AMBYcEdw4N9fqzyk0v2XthU9auWZLS++JGJQr83QfWo6fL19SdDfXcwCGJa9Zj6eE5hV2tyRij/V+D0zg=="
  "resolved" "https://registry.npmjs.org/@svgr/babel-preset/-/babel-preset-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute" "^6.0.0"
    "@svgr/babel-plugin-remove-jsx-attribute" "^6.0.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression" "^6.0.0"
    "@svgr/babel-plugin-replace-jsx-attribute-value" "^6.0.0"
    "@svgr/babel-plugin-svg-dynamic-title" "^6.0.0"
    "@svgr/babel-plugin-svg-em-dimensions" "^6.0.0"
    "@svgr/babel-plugin-transform-react-native-svg" "^6.0.0"
    "@svgr/babel-plugin-transform-svg-component" "^6.0.0"

"@svgr/core@^5.5.0":
  "integrity" "sha512-q52VOcsJPvV3jO1wkPtzTuKlvX7Y3xIcWRpCMtBF3MrteZJtBfQw/+u0B1BHy5ColpQc1/YVTrPEtSYIMNZlrQ=="
  "resolved" "https://registry.npmjs.org/@svgr/core/-/core-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@svgr/plugin-jsx" "^5.5.0"
    "camelcase" "^6.2.0"
    "cosmiconfig" "^7.0.0"

"@svgr/core@^6.0.0", "@svgr/core@^6.0.0-alpha.0":
  "integrity" "sha512-5xsfSJ25/HHgEV3DPG585bxlgtjzdUtZUcJDgXX8RwXQ48x+ZOinPI7OkEpZ5s1ilPVnK3+pDVp1A5S00cxRRA=="
  "resolved" "https://registry.npmjs.org/@svgr/core/-/core-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@svgr/plugin-jsx" "^6.0.0"
    "camelcase" "^6.2.0"
    "cosmiconfig" "^7.0.1"

"@svgr/hast-util-to-babel-ast@^5.5.0":
  "integrity" "sha512-cAaR/CAiZRB8GP32N+1jocovUtvlj0+e65TB50/6Lcime+EA49m/8l+P2ko+XPJ4dw3xaPS3jOL4F2X4KWxoeQ=="
  "resolved" "https://registry.npmjs.org/@svgr/hast-util-to-babel-ast/-/hast-util-to-babel-ast-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@babel/types" "^7.12.6"

"@svgr/hast-util-to-babel-ast@^6.0.0":
  "integrity" "sha512-S+TxtCdDyRGafH1VG1t/uPZ87aOYOHzWL8kqz4FoSZcIbzWA6rnOmjNViNiDzqmEpzp2PW5o5mZfvC9DiVZhTQ=="
  "resolved" "https://registry.npmjs.org/@svgr/hast-util-to-babel-ast/-/hast-util-to-babel-ast-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@babel/types" "^7.15.6"
    "entities" "^3.0.1"

"@svgr/plugin-jsx@^5.5.0":
  "integrity" "sha512-V/wVh33j12hGh05IDg8GpIUXbjAPnTdPTKuP4VNLggnwaHMPNQNae2pRnyTAILWCQdz5GyMqtO488g7CKM8CBA=="
  "resolved" "https://registry.npmjs.org/@svgr/plugin-jsx/-/plugin-jsx-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@babel/core" "^7.12.3"
    "@svgr/babel-preset" "^5.5.0"
    "@svgr/hast-util-to-babel-ast" "^5.5.0"
    "svg-parser" "^2.0.2"

"@svgr/plugin-jsx@^6.0.0":
  "integrity" "sha512-yr/+so84htzdGXAOU4I7ggLEEcVAT1kNn+tl8YJuopVycrPM6iaG4yrTzeNBKW3FiECy/JR+c4bN7Jldr2UdEQ=="
  "resolved" "https://registry.npmjs.org/@svgr/plugin-jsx/-/plugin-jsx-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@babel/core" "^7.15.5"
    "@svgr/babel-preset" "^6.0.0"
    "@svgr/hast-util-to-babel-ast" "^6.0.0"
    "svg-parser" "^2.0.2"

"@svgr/plugin-svgo@^5.5.0":
  "integrity" "sha512-r5swKk46GuQl4RrVejVwpeeJaydoxkdwkM1mBKOgJLBUJPGaLci6ylg/IjhrRsREKDkr4kbMWdgOtbXEh0fyLQ=="
  "resolved" "https://registry.npmjs.org/@svgr/plugin-svgo/-/plugin-svgo-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "cosmiconfig" "^7.0.0"
    "deepmerge" "^4.2.2"
    "svgo" "^1.2.2"

"@svgr/webpack@^5.5.0":
  "integrity" "sha512-DOBOK255wfQxguUta2INKkzPj6AIS6iafZYiYmHn6W3pHlycSRRlvWKCfLDG10fXfLWqE3DJHgRUOyJYmARa7g=="
  "resolved" "https://registry.npmjs.org/@svgr/webpack/-/webpack-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/plugin-transform-react-constant-elements" "^7.12.1"
    "@babel/preset-env" "^7.12.1"
    "@babel/preset-react" "^7.12.5"
    "@svgr/core" "^5.5.0"
    "@svgr/plugin-jsx" "^5.5.0"
    "@svgr/plugin-svgo" "^5.5.0"
    "loader-utils" "^2.0.0"

"@turf/along@^6.5.0":
  "integrity" "sha512-LLyWQ0AARqJCmMcIEAXF4GEu8usmd4Kbz3qk1Oy5HoRNpZX47+i5exQtmIWKdqJ1MMhW26fCTXgpsEs5zgJ5gw=="
  "resolved" "https://registry.npmjs.org/@turf/along/-/along-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bearing" "^6.5.0"
    "@turf/destination" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/angle@^6.5.0":
  "integrity" "sha512-4pXMbWhFofJJAOvTMCns6N4C8CMd5Ih4O2jSAG9b3dDHakj3O4yN1+Zbm+NUei+eVEZ9gFeVp9svE3aMDenIkw=="
  "resolved" "https://registry.npmjs.org/@turf/angle/-/angle-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bearing" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/rhumb-bearing" "^6.5.0"

"@turf/area@^6.5.0":
  "integrity" "sha512-xCZdiuojokLbQ+29qR6qoMD89hv+JAgWjLrwSEWL+3JV8IXKeNFl6XkEJz9HGkVpnXvQKJoRz4/liT+8ZZ5Jyg=="
  "resolved" "https://registry.npmjs.org/@turf/area/-/area-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/bbox-clip@^6.5.0":
  "integrity" "sha512-F6PaIRF8WMp8EmgU/Ke5B1Y6/pia14UAYB5TiBC668w5rVVjy5L8rTm/m2lEkkDMHlzoP9vNY4pxpNthE7rLcQ=="
  "resolved" "https://registry.npmjs.org/@turf/bbox-clip/-/bbox-clip-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/bbox-polygon@^6.5.0":
  "integrity" "sha512-+/r0NyL1lOG3zKZmmf6L8ommU07HliP4dgYToMoTxqzsWzyLjaj/OzgQ8rBmv703WJX+aS6yCmLuIhYqyufyuw=="
  "resolved" "https://registry.npmjs.org/@turf/bbox-polygon/-/bbox-polygon-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/bbox@*", "@turf/bbox@^6.5.0":
  "integrity" "sha512-RBbLaao5hXTYyyg577iuMtDB8ehxMlUqHEJiMs8jT1GHkFhr6sYre3lmLsPeYEi/ZKj5TP5tt7fkzNdJ4GIVyw=="
  "resolved" "https://registry.npmjs.org/@turf/bbox/-/bbox-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/bearing@^6.5.0":
  "integrity" "sha512-dxINYhIEMzgDOztyMZc20I7ssYVNEpSv04VbMo5YPQsqa80KO3TFvbuCahMsCAW5z8Tncc8dwBlEFrmRjJG33A=="
  "resolved" "https://registry.npmjs.org/@turf/bearing/-/bearing-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/bezier-spline@^6.5.0":
  "integrity" "sha512-vokPaurTd4PF96rRgGVm6zYYC5r1u98ZsG+wZEv9y3kJTuJRX/O3xIY2QnTGTdbVmAJN1ouOsD0RoZYaVoXORQ=="
  "resolved" "https://registry.npmjs.org/@turf/bezier-spline/-/bezier-spline-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/boolean-clockwise@^6.5.0":
  "integrity" "sha512-45+C7LC5RMbRWrxh3Z0Eihsc8db1VGBO5d9BLTOAwU4jR6SgsunTfRWR16X7JUwIDYlCVEmnjcXJNi/kIU3VIw=="
  "resolved" "https://registry.npmjs.org/@turf/boolean-clockwise/-/boolean-clockwise-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/boolean-contains@^6.5.0":
  "integrity" "sha512-4m8cJpbw+YQcKVGi8y0cHhBUnYT+QRfx6wzM4GI1IdtYH3p4oh/DOBJKrepQyiDzFDaNIjxuWXBh0ai1zVwOQQ=="
  "resolved" "https://registry.npmjs.org/@turf/boolean-contains/-/boolean-contains-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/boolean-point-on-line" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/boolean-crosses@^6.5.0":
  "integrity" "sha512-gvshbTPhAHporTlQwBJqyfW+2yV8q/mOTxG6PzRVl6ARsqNoqYQWkd4MLug7OmAqVyBzLK3201uAeBjxbGw0Ng=="
  "resolved" "https://registry.npmjs.org/@turf/boolean-crosses/-/boolean-crosses-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-intersect" "^6.5.0"
    "@turf/polygon-to-line" "^6.5.0"

"@turf/boolean-disjoint@^6.5.0":
  "integrity" "sha512-rZ2ozlrRLIAGo2bjQ/ZUu4oZ/+ZjGvLkN5CKXSKBcu6xFO6k2bgqeM8a1836tAW+Pqp/ZFsTA5fZHsJZvP2D5g=="
  "resolved" "https://registry.npmjs.org/@turf/boolean-disjoint/-/boolean-disjoint-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/line-intersect" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/polygon-to-line" "^6.5.0"

"@turf/boolean-equal@^6.5.0":
  "integrity" "sha512-cY0M3yoLC26mhAnjv1gyYNQjn7wxIXmL2hBmI/qs8g5uKuC2hRWi13ydufE3k4x0aNRjFGlg41fjoYLwaVF+9Q=="
  "resolved" "https://registry.npmjs.org/@turf/boolean-equal/-/boolean-equal-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/clean-coords" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "geojson-equality" "0.1.6"

"@turf/boolean-intersects@^6.5.0":
  "integrity" "sha512-nIxkizjRdjKCYFQMnml6cjPsDOBCThrt+nkqtSEcxkKMhAQj5OO7o2CecioNTaX8EayqwMGVKcsz27oP4mKPTw=="
  "resolved" "https://registry.npmjs.org/@turf/boolean-intersects/-/boolean-intersects-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/boolean-disjoint" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/boolean-overlap@^6.5.0":
  "integrity" "sha512-8btMIdnbXVWUa1M7D4shyaSGxLRw6NjMcqKBcsTXcZdnaixl22k7ar7BvIzkaRYN3SFECk9VGXfLncNS3ckQUw=="
  "resolved" "https://registry.npmjs.org/@turf/boolean-overlap/-/boolean-overlap-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-intersect" "^6.5.0"
    "@turf/line-overlap" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "geojson-equality" "0.1.6"

"@turf/boolean-parallel@^6.5.0":
  "integrity" "sha512-aSHJsr1nq9e5TthZGZ9CZYeXklJyRgR5kCLm5X4urz7+MotMOp/LsGOsvKvK9NeUl9+8OUmfMn8EFTT8LkcvIQ=="
  "resolved" "https://registry.npmjs.org/@turf/boolean-parallel/-/boolean-parallel-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/clean-coords" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/line-segment" "^6.5.0"
    "@turf/rhumb-bearing" "^6.5.0"

"@turf/boolean-point-in-polygon@^6.5.0":
  "integrity" "sha512-DtSuVFB26SI+hj0SjrvXowGTUCHlgevPAIsukssW6BG5MlNSBQAo70wpICBNJL6RjukXg8d2eXaAWuD/CqL00A=="
  "resolved" "https://registry.npmjs.org/@turf/boolean-point-in-polygon/-/boolean-point-in-polygon-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/boolean-point-on-line@^6.5.0":
  "integrity" "sha512-A1BbuQ0LceLHvq7F/P7w3QvfpmZqbmViIUPHdNLvZimFNLo4e6IQunmzbe+8aSStH9QRZm3VOflyvNeXvvpZEQ=="
  "resolved" "https://registry.npmjs.org/@turf/boolean-point-on-line/-/boolean-point-on-line-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/boolean-within@^6.5.0":
  "integrity" "sha512-YQB3oU18Inx35C/LU930D36RAVe7LDXk1kWsQ8mLmuqYn9YdPsDQTMTkLJMhoQ8EbN7QTdy333xRQ4MYgToteQ=="
  "resolved" "https://registry.npmjs.org/@turf/boolean-within/-/boolean-within-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/boolean-point-on-line" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/buffer@^6.5.0":
  "integrity" "sha512-qeX4N6+PPWbKqp1AVkBVWFerGjMYMUyencwfnkCesoznU6qvfugFHNAngNqIBVnJjZ5n8IFyOf+akcxnrt9sNg=="
  "resolved" "https://registry.npmjs.org/@turf/buffer/-/buffer-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/center" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/projection" "^6.5.0"
    "d3-geo" "1.7.1"
    "turf-jsts" "*"

"@turf/center-mean@^6.5.0":
  "integrity" "sha512-AAX6f4bVn12pTVrMUiB9KrnV94BgeBKpyg3YpfnEbBpkN/znfVhL8dG8IxMAxAoSZ61Zt9WLY34HfENveuOZ7Q=="
  "resolved" "https://registry.npmjs.org/@turf/center-mean/-/center-mean-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/center-median@^6.5.0":
  "integrity" "sha512-dT8Ndu5CiZkPrj15PBvslpuf01ky41DEYEPxS01LOxp5HOUHXp1oJxsPxvc+i/wK4BwccPNzU1vzJ0S4emd1KQ=="
  "resolved" "https://registry.npmjs.org/@turf/center-median/-/center-median-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/center-mean" "^6.5.0"
    "@turf/centroid" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/center-of-mass@^6.5.0":
  "integrity" "sha512-EWrriU6LraOfPN7m1jZi+1NLTKNkuIsGLZc2+Y8zbGruvUW+QV7K0nhf7iZWutlxHXTBqEXHbKue/o79IumAsQ=="
  "resolved" "https://registry.npmjs.org/@turf/center-of-mass/-/center-of-mass-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/centroid" "^6.5.0"
    "@turf/convex" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/center@^6.5.0":
  "integrity" "sha512-T8KtMTfSATWcAX088rEDKjyvQCBkUsLnK/Txb6/8WUXIeOZyHu42G7MkdkHRoHtwieLdduDdmPLFyTdG5/e7ZQ=="
  "resolved" "https://registry.npmjs.org/@turf/center/-/center-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/centroid@^6.5.0":
  "integrity" "sha512-MwE1oq5E3isewPprEClbfU5pXljIK/GUOMbn22UM3IFPDJX0KeoyLNwghszkdmFp/qMGL/M13MMWvU+GNLXP/A=="
  "resolved" "https://registry.npmjs.org/@turf/centroid/-/centroid-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/circle@^6.5.0":
  "integrity" "sha512-oU1+Kq9DgRnoSbWFHKnnUdTmtcRUMmHoV9DjTXu9vOLNV5OWtAAh1VZ+mzsioGGzoDNT/V5igbFOkMfBQc0B6A=="
  "resolved" "https://registry.npmjs.org/@turf/circle/-/circle-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/destination" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/clean-coords@^6.5.0":
  "integrity" "sha512-EMX7gyZz0WTH/ET7xV8MyrExywfm9qUi0/MY89yNffzGIEHuFfqwhcCqZ8O00rZIPZHUTxpmsxQSTfzJJA1CPw=="
  "resolved" "https://registry.npmjs.org/@turf/clean-coords/-/clean-coords-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/clone@^6.5.0":
  "integrity" "sha512-mzVtTFj/QycXOn6ig+annKrM6ZlimreKYz6f/GSERytOpgzodbQyOgkfwru100O1KQhhjSudKK4DsQ0oyi9cTw=="
  "resolved" "https://registry.npmjs.org/@turf/clone/-/clone-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/clusters-dbscan@^6.5.0":
  "integrity" "sha512-SxZEE4kADU9DqLRiT53QZBBhu8EP9skviSyl+FGj08Y01xfICM/RR9ACUdM0aEQimhpu+ZpRVcUK+2jtiCGrYQ=="
  "resolved" "https://registry.npmjs.org/@turf/clusters-dbscan/-/clusters-dbscan-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "density-clustering" "1.3.0"

"@turf/clusters-kmeans@^6.5.0":
  "integrity" "sha512-DwacD5+YO8kwDPKaXwT9DV46tMBVNsbi1IzdajZu1JDSWoN7yc7N9Qt88oi+p30583O0UPVkAK+A10WAQv4mUw=="
  "resolved" "https://registry.npmjs.org/@turf/clusters-kmeans/-/clusters-kmeans-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "skmeans" "0.9.7"

"@turf/clusters@^6.5.0":
  "integrity" "sha512-Y6gfnTJzQ1hdLfCsyd5zApNbfLIxYEpmDibHUqR5z03Lpe02pa78JtgrgUNt1seeO/aJ4TG1NLN8V5gOrHk04g=="
  "resolved" "https://registry.npmjs.org/@turf/clusters/-/clusters-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/collect@^6.5.0":
  "integrity" "sha512-4dN/T6LNnRg099m97BJeOcTA5fSI8cu87Ydgfibewd2KQwBexO69AnjEFqfPX3Wj+Zvisj1uAVIZbPmSSrZkjg=="
  "resolved" "https://registry.npmjs.org/@turf/collect/-/collect-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "rbush" "2.x"

"@turf/combine@^6.5.0":
  "integrity" "sha512-Q8EIC4OtAcHiJB3C4R+FpB4LANiT90t17uOd851qkM2/o6m39bfN5Mv0PWqMZIHWrrosZqRqoY9dJnzz/rJxYQ=="
  "resolved" "https://registry.npmjs.org/@turf/combine/-/combine-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/concave@^6.5.0":
  "integrity" "sha512-I/sUmUC8TC5h/E2vPwxVht+nRt+TnXIPRoztDFvS8/Y0+cBDple9inLSo9nnPXMXidrBlGXZ9vQx/BjZUJgsRQ=="
  "resolved" "https://registry.npmjs.org/@turf/concave/-/concave-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/tin" "^6.5.0"
    "topojson-client" "3.x"
    "topojson-server" "3.x"

"@turf/convex@^6.5.0":
  "integrity" "sha512-x7ZwC5z7PJB0SBwNh7JCeCNx7Iu+QSrH7fYgK0RhhNop13TqUlvHMirMLRgf2db1DqUetrAO2qHJeIuasquUWg=="
  "resolved" "https://registry.npmjs.org/@turf/convex/-/convex-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "concaveman" "*"

"@turf/destination@^6.5.0":
  "integrity" "sha512-4cnWQlNC8d1tItOz9B4pmJdWpXqS0vEvv65bI/Pj/genJnsL7evI0/Xw42RvEGROS481MPiU80xzvwxEvhQiMQ=="
  "resolved" "https://registry.npmjs.org/@turf/destination/-/destination-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/difference@^6.5.0":
  "integrity" "sha512-l8iR5uJqvI+5Fs6leNbhPY5t/a3vipUF/3AeVLpwPQcgmedNXyheYuy07PcMGH5Jdpi5gItOiTqwiU/bUH4b3A=="
  "resolved" "https://registry.npmjs.org/@turf/difference/-/difference-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "polygon-clipping" "^0.15.3"

"@turf/dissolve@^6.5.0":
  "integrity" "sha512-WBVbpm9zLTp0Bl9CE35NomTaOL1c4TQCtEoO43YaAhNEWJOOIhZMFJyr8mbvYruKl817KinT3x7aYjjCMjTAsQ=="
  "resolved" "https://registry.npmjs.org/@turf/dissolve/-/dissolve-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "polygon-clipping" "^0.15.3"

"@turf/distance-weight@^6.5.0":
  "integrity" "sha512-a8qBKkgVNvPKBfZfEJZnC3DV7dfIsC3UIdpRci/iap/wZLH41EmS90nM+BokAJflUHYy8PqE44wySGWHN1FXrQ=="
  "resolved" "https://registry.npmjs.org/@turf/distance-weight/-/distance-weight-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/centroid" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/distance@^6.5.0":
  "integrity" "sha512-xzykSLfoURec5qvQJcfifw/1mJa+5UwByZZ5TZ8iaqjGYN0vomhV9aiSLeYdUGtYRESZ+DYC/OzY+4RclZYgMg=="
  "resolved" "https://registry.npmjs.org/@turf/distance/-/distance-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/ellipse@^6.5.0":
  "integrity" "sha512-kuXtwFviw/JqnyJXF1mrR/cb496zDTSbGKtSiolWMNImYzGGkbsAsFTjwJYgD7+4FixHjp0uQPzo70KDf3AIBw=="
  "resolved" "https://registry.npmjs.org/@turf/ellipse/-/ellipse-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/rhumb-destination" "^6.5.0"
    "@turf/transform-rotate" "^6.5.0"

"@turf/envelope@^6.5.0":
  "integrity" "sha512-9Z+FnBWvOGOU4X+fMZxYFs1HjFlkKqsddLuMknRaqcJd6t+NIv5DWvPtDL8ATD2GEExYDiFLwMdckfr1yqJgHA=="
  "resolved" "https://registry.npmjs.org/@turf/envelope/-/envelope-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/bbox-polygon" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/explode@^6.5.0":
  "integrity" "sha512-6cSvMrnHm2qAsace6pw9cDmK2buAlw8+tjeJVXMfMyY+w7ZUi1rprWMsY92J7s2Dar63Bv09n56/1V7+tcj52Q=="
  "resolved" "https://registry.npmjs.org/@turf/explode/-/explode-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/flatten@^6.5.0":
  "integrity" "sha512-IBZVwoNLVNT6U/bcUUllubgElzpMsNoCw8tLqBw6dfYg9ObGmpEjf9BIYLr7a2Yn5ZR4l7YIj2T7kD5uJjZADQ=="
  "resolved" "https://registry.npmjs.org/@turf/flatten/-/flatten-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/flip@^6.5.0":
  "integrity" "sha512-oyikJFNjt2LmIXQqgOGLvt70RgE2lyzPMloYWM7OR5oIFGRiBvqVD2hA6MNw6JewIm30fWZ8DQJw1NHXJTJPbg=="
  "resolved" "https://registry.npmjs.org/@turf/flip/-/flip-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/great-circle@^6.5.0":
  "integrity" "sha512-7ovyi3HaKOXdFyN7yy1yOMa8IyOvV46RC1QOQTT+RYUN8ke10eyqExwBpL9RFUPvlpoTzoYbM/+lWPogQlFncg=="
  "resolved" "https://registry.npmjs.org/@turf/great-circle/-/great-circle-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/helpers@^6.5.0", "@turf/helpers@6.x":
  "integrity" "sha512-VbI1dV5bLFzohYYdgqwikdMVpe7pJ9X3E+dlr425wa2/sMJqYDhTO++ec38/pcPvPE6oD9WEEeU3Xu3gza+VPw=="
  "resolved" "https://registry.npmjs.org/@turf/helpers/-/helpers-6.5.0.tgz"
  "version" "6.5.0"

"@turf/hex-grid@^6.5.0":
  "integrity" "sha512-Ln3tc2tgZT8etDOldgc6e741Smg1CsMKAz1/Mlel+MEL5Ynv2mhx3m0q4J9IB1F3a4MNjDeVvm8drAaf9SF33g=="
  "resolved" "https://registry.npmjs.org/@turf/hex-grid/-/hex-grid-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/intersect" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/interpolate@^6.5.0":
  "integrity" "sha512-LSH5fMeiGyuDZ4WrDJNgh81d2DnNDUVJtuFryJFup8PV8jbs46lQGfI3r1DJ2p1IlEJIz3pmAZYeTfMMoeeohw=="
  "resolved" "https://registry.npmjs.org/@turf/interpolate/-/interpolate-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/centroid" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/hex-grid" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/point-grid" "^6.5.0"
    "@turf/square-grid" "^6.5.0"
    "@turf/triangle-grid" "^6.5.0"

"@turf/intersect@^6.5.0":
  "integrity" "sha512-2legGJeKrfFkzntcd4GouPugoqPUjexPZnOvfez+3SfIMrHvulw8qV8u7pfVyn2Yqs53yoVCEjS5sEpvQ5YRQg=="
  "resolved" "https://registry.npmjs.org/@turf/intersect/-/intersect-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "polygon-clipping" "^0.15.3"

"@turf/invariant@^6.5.0":
  "integrity" "sha512-Wv8PRNCtPD31UVbdJE/KVAWKe7l6US+lJItRR/HOEW3eh+U/JwRCSUl/KZ7bmjM/C+zLNoreM2TU6OoLACs4eg=="
  "resolved" "https://registry.npmjs.org/@turf/invariant/-/invariant-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/isobands@^6.5.0":
  "integrity" "sha512-4h6sjBPhRwMVuFaVBv70YB7eGz+iw0bhPRnp+8JBdX1UPJSXhoi/ZF2rACemRUr0HkdVB/a1r9gC32vn5IAEkw=="
  "resolved" "https://registry.npmjs.org/@turf/isobands/-/isobands-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/area" "^6.5.0"
    "@turf/bbox" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/explode" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "object-assign" "*"

"@turf/isolines@^6.5.0":
  "integrity" "sha512-6ElhiLCopxWlv4tPoxiCzASWt/jMRvmp6mRYrpzOm3EUl75OhHKa/Pu6Y9nWtCMmVC/RcWtiiweUocbPLZLm0A=="
  "resolved" "https://registry.npmjs.org/@turf/isolines/-/isolines-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "object-assign" "*"

"@turf/kinks@^6.5.0":
  "integrity" "sha512-ViCngdPt1eEL7hYUHR2eHR662GvCgTc35ZJFaNR6kRtr6D8plLaDju0FILeFFWSc+o8e3fwxZEJKmFj9IzPiIQ=="
  "resolved" "https://registry.npmjs.org/@turf/kinks/-/kinks-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/length@^6.5.0":
  "integrity" "sha512-5pL5/pnw52fck3oRsHDcSGrj9HibvtlrZ0QNy2OcW8qBFDNgZ4jtl6U7eATVoyWPKBHszW3dWETW+iLV7UARig=="
  "resolved" "https://registry.npmjs.org/@turf/length/-/length-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/line-arc@^6.5.0":
  "integrity" "sha512-I6c+V6mIyEwbtg9P9zSFF89T7QPe1DPTG3MJJ6Cm1MrAY0MdejwQKOpsvNl8LDU2ekHOlz2kHpPVR7VJsoMllA=="
  "resolved" "https://registry.npmjs.org/@turf/line-arc/-/line-arc-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/circle" "^6.5.0"
    "@turf/destination" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/line-chunk@^6.5.0":
  "integrity" "sha512-i1FGE6YJaaYa+IJesTfyRRQZP31QouS+wh/pa6O3CC0q4T7LtHigyBSYjrbjSLfn2EVPYGlPCMFEqNWCOkC6zg=="
  "resolved" "https://registry.npmjs.org/@turf/line-chunk/-/line-chunk-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/length" "^6.5.0"
    "@turf/line-slice-along" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/line-intersect@^6.5.0":
  "integrity" "sha512-CS6R1tZvVQD390G9Ea4pmpM6mJGPWoL82jD46y0q1KSor9s6HupMIo1kY4Ny+AEYQl9jd21V3Scz20eldpbTVA=="
  "resolved" "https://registry.npmjs.org/@turf/line-intersect/-/line-intersect-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-segment" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "geojson-rbush" "3.x"

"@turf/line-offset@^6.5.0":
  "integrity" "sha512-CEXZbKgyz8r72qRvPchK0dxqsq8IQBdH275FE6o4MrBkzMcoZsfSjghtXzKaz9vvro+HfIXal0sTk2mqV1lQTw=="
  "resolved" "https://registry.npmjs.org/@turf/line-offset/-/line-offset-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/line-overlap@^6.5.0":
  "integrity" "sha512-xHOaWLd0hkaC/1OLcStCpfq55lPHpPNadZySDXYiYjEz5HXr1oKmtMYpn0wGizsLwrOixRdEp+j7bL8dPt4ojQ=="
  "resolved" "https://registry.npmjs.org/@turf/line-overlap/-/line-overlap-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/boolean-point-on-line" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-segment" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/nearest-point-on-line" "^6.5.0"
    "deep-equal" "1.x"
    "geojson-rbush" "3.x"

"@turf/line-segment@^6.5.0":
  "integrity" "sha512-jI625Ho4jSuJESNq66Mmi290ZJ5pPZiQZruPVpmHkUw257Pew0alMmb6YrqYNnLUuiVVONxAAKXUVeeUGtycfw=="
  "resolved" "https://registry.npmjs.org/@turf/line-segment/-/line-segment-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/line-slice-along@^6.5.0":
  "integrity" "sha512-KHJRU6KpHrAj+BTgTNqby6VCTnDzG6a1sJx/I3hNvqMBLvWVA2IrkR9L9DtsQsVY63IBwVdQDqiwCuZLDQh4Ng=="
  "resolved" "https://registry.npmjs.org/@turf/line-slice-along/-/line-slice-along-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bearing" "^6.5.0"
    "@turf/destination" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/line-slice@^6.5.0":
  "integrity" "sha512-vDqJxve9tBHhOaVVFXqVjF5qDzGtKWviyjbyi2QnSnxyFAmLlLnBfMX8TLQCAf2GxHibB95RO5FBE6I2KVPRuw=="
  "resolved" "https://registry.npmjs.org/@turf/line-slice/-/line-slice-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/nearest-point-on-line" "^6.5.0"

"@turf/line-split@^6.5.0":
  "integrity" "sha512-/rwUMVr9OI2ccJjw7/6eTN53URtGThNSD5I0GgxyFXMtxWiloRJ9MTff8jBbtPWrRka/Sh2GkwucVRAEakx9Sw=="
  "resolved" "https://registry.npmjs.org/@turf/line-split/-/line-split-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-intersect" "^6.5.0"
    "@turf/line-segment" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/nearest-point-on-line" "^6.5.0"
    "@turf/square" "^6.5.0"
    "@turf/truncate" "^6.5.0"
    "geojson-rbush" "3.x"

"@turf/line-to-polygon@^6.5.0":
  "integrity" "sha512-qYBuRCJJL8Gx27OwCD1TMijM/9XjRgXH/m/TyuND4OXedBpIWlK5VbTIO2gJ8OCfznBBddpjiObLBrkuxTpN4Q=="
  "resolved" "https://registry.npmjs.org/@turf/line-to-polygon/-/line-to-polygon-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/mask@^6.5.0":
  "integrity" "sha512-RQha4aU8LpBrmrkH8CPaaoAfk0Egj5OuXtv6HuCQnHeGNOQt3TQVibTA3Sh4iduq4EPxnZfDjgsOeKtrCA19lg=="
  "resolved" "https://registry.npmjs.org/@turf/mask/-/mask-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "polygon-clipping" "^0.15.3"

"@turf/meta@^6.5.0", "@turf/meta@6.x":
  "integrity" "sha512-RrArvtsV0vdsCBegoBtOalgdSOfkBrTJ07VkpiCnq/491W67hnMWmDu7e6Ztw0C3WldRYTXkg3SumfdzZxLBHA=="
  "resolved" "https://registry.npmjs.org/@turf/meta/-/meta-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/midpoint@^6.5.0":
  "integrity" "sha512-MyTzV44IwmVI6ec9fB2OgZ53JGNlgOpaYl9ArKoF49rXpL84F9rNATndbe0+MQIhdkw8IlzA6xVP4lZzfMNVCw=="
  "resolved" "https://registry.npmjs.org/@turf/midpoint/-/midpoint-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bearing" "^6.5.0"
    "@turf/destination" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/moran-index@^6.5.0":
  "integrity" "sha512-ItsnhrU2XYtTtTudrM8so4afBCYWNaB0Mfy28NZwLjB5jWuAsvyV+YW+J88+neK/ougKMTawkmjQqodNJaBeLQ=="
  "resolved" "https://registry.npmjs.org/@turf/moran-index/-/moran-index-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/distance-weight" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/nearest-point-on-line@^6.5.0":
  "integrity" "sha512-WthrvddddvmymnC+Vf7BrkHGbDOUu6Z3/6bFYUGv1kxw8tiZ6n83/VG6kHz4poHOfS0RaNflzXSkmCi64fLBlg=="
  "resolved" "https://registry.npmjs.org/@turf/nearest-point-on-line/-/nearest-point-on-line-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bearing" "^6.5.0"
    "@turf/destination" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-intersect" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/nearest-point-to-line@^6.5.0":
  "integrity" "sha512-PXV7cN0BVzUZdjj6oeb/ESnzXSfWmEMrsfZSDRgqyZ9ytdiIj/eRsnOXLR13LkTdXVOJYDBuf7xt1mLhM4p6+Q=="
  "resolved" "https://registry.npmjs.org/@turf/nearest-point-to-line/-/nearest-point-to-line-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/point-to-line-distance" "^6.5.0"
    "object-assign" "*"

"@turf/nearest-point@^6.5.0":
  "integrity" "sha512-fguV09QxilZv/p94s8SMsXILIAMiaXI5PATq9d7YWijLxWUj6Q/r43kxyoi78Zmwwh1Zfqz9w+bCYUAxZ5+euA=="
  "resolved" "https://registry.npmjs.org/@turf/nearest-point/-/nearest-point-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/planepoint@^6.5.0":
  "integrity" "sha512-R3AahA6DUvtFbka1kcJHqZ7DMHmPXDEQpbU5WaglNn7NaCQg9HB0XM0ZfqWcd5u92YXV+Gg8QhC8x5XojfcM4Q=="
  "resolved" "https://registry.npmjs.org/@turf/planepoint/-/planepoint-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/point-grid@^6.5.0":
  "integrity" "sha512-Iq38lFokNNtQJnOj/RBKmyt6dlof0yhaHEDELaWHuECm1lIZLY3ZbVMwbs+nXkwTAHjKfS/OtMheUBkw+ee49w=="
  "resolved" "https://registry.npmjs.org/@turf/point-grid/-/point-grid-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/boolean-within" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/point-on-feature@^6.5.0":
  "integrity" "sha512-bDpuIlvugJhfcF/0awAQ+QI6Om1Y1FFYE8Y/YdxGRongivix850dTeXCo0mDylFdWFPGDo7Mmh9Vo4VxNwW/TA=="
  "resolved" "https://registry.npmjs.org/@turf/point-on-feature/-/point-on-feature-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/center" "^6.5.0"
    "@turf/explode" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/nearest-point" "^6.5.0"

"@turf/point-to-line-distance@^6.5.0":
  "integrity" "sha512-opHVQ4vjUhNBly1bob6RWy+F+hsZDH9SA0UW36pIRzfpu27qipU18xup0XXEePfY6+wvhF6yL/WgCO2IbrLqEA=="
  "resolved" "https://registry.npmjs.org/@turf/point-to-line-distance/-/point-to-line-distance-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bearing" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/projection" "^6.5.0"
    "@turf/rhumb-bearing" "^6.5.0"
    "@turf/rhumb-distance" "^6.5.0"

"@turf/points-within-polygon@^6.5.0":
  "integrity" "sha512-YyuheKqjliDsBDt3Ho73QVZk1VXX1+zIA2gwWvuz8bR1HXOkcuwk/1J76HuFMOQI3WK78wyAi+xbkx268PkQzQ=="
  "resolved" "https://registry.npmjs.org/@turf/points-within-polygon/-/points-within-polygon-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/polygon-smooth@^6.5.0":
  "integrity" "sha512-LO/X/5hfh/Rk4EfkDBpLlVwt3i6IXdtQccDT9rMjXEP32tRgy0VMFmdkNaXoGlSSKf/1mGqLl4y4wHd86DqKbg=="
  "resolved" "https://registry.npmjs.org/@turf/polygon-smooth/-/polygon-smooth-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/polygon-tangents@^6.5.0":
  "integrity" "sha512-sB4/IUqJMYRQH9jVBwqS/XDitkEfbyqRy+EH/cMRJURTg78eHunvJ708x5r6umXsbiUyQU4eqgPzEylWEQiunw=="
  "resolved" "https://registry.npmjs.org/@turf/polygon-tangents/-/polygon-tangents-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/boolean-within" "^6.5.0"
    "@turf/explode" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/nearest-point" "^6.5.0"

"@turf/polygon-to-line@^6.5.0":
  "integrity" "sha512-5p4n/ij97EIttAq+ewSnKt0ruvuM+LIDzuczSzuHTpq4oS7Oq8yqg5TQ4nzMVuK41r/tALCk7nAoBuw3Su4Gcw=="
  "resolved" "https://registry.npmjs.org/@turf/polygon-to-line/-/polygon-to-line-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/polygonize@^6.5.0":
  "integrity" "sha512-a/3GzHRaCyzg7tVYHo43QUChCspa99oK4yPqooVIwTC61npFzdrmnywMv0S+WZjHZwK37BrFJGFrZGf6ocmY5w=="
  "resolved" "https://registry.npmjs.org/@turf/polygonize/-/polygonize-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/envelope" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/projection@^6.5.0":
  "integrity" "sha512-/Pgh9mDvQWWu8HRxqpM+tKz8OzgauV+DiOcr3FCjD6ubDnrrmMJlsf6fFJmggw93mtVPrZRL6yyi9aYCQBOIvg=="
  "resolved" "https://registry.npmjs.org/@turf/projection/-/projection-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/random@^6.5.0":
  "integrity" "sha512-8Q25gQ/XbA7HJAe+eXp4UhcXM9aOOJFaxZ02+XSNwMvY8gtWSCBLVqRcW4OhqilgZ8PeuQDWgBxeo+BIqqFWFQ=="
  "resolved" "https://registry.npmjs.org/@turf/random/-/random-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/rectangle-grid@^6.5.0":
  "integrity" "sha512-yQZ/1vbW68O2KsSB3OZYK+72aWz/Adnf7m2CMKcC+aq6TwjxZjAvlbCOsNUnMAuldRUVN1ph6RXMG4e9KEvKvg=="
  "resolved" "https://registry.npmjs.org/@turf/rectangle-grid/-/rectangle-grid-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/boolean-intersects" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/rewind@^6.5.0":
  "integrity" "sha512-IoUAMcHWotBWYwSYuYypw/LlqZmO+wcBpn8ysrBNbazkFNkLf3btSDZMkKJO/bvOzl55imr/Xj4fi3DdsLsbzQ=="
  "resolved" "https://registry.npmjs.org/@turf/rewind/-/rewind-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/boolean-clockwise" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/rhumb-bearing@^6.5.0":
  "integrity" "sha512-jMyqiMRK4hzREjQmnLXmkJ+VTNTx1ii8vuqRwJPcTlKbNWfjDz/5JqJlb5NaFDcdMpftWovkW5GevfnuzHnOYA=="
  "resolved" "https://registry.npmjs.org/@turf/rhumb-bearing/-/rhumb-bearing-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/rhumb-destination@^6.5.0":
  "integrity" "sha512-RHNP1Oy+7xTTdRrTt375jOZeHceFbjwohPHlr9Hf68VdHHPMAWgAKqiX2YgSWDcvECVmiGaBKWus1Df+N7eE4Q=="
  "resolved" "https://registry.npmjs.org/@turf/rhumb-destination/-/rhumb-destination-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/rhumb-distance@^6.5.0":
  "integrity" "sha512-oKp8KFE8E4huC2Z1a1KNcFwjVOqa99isxNOwfo4g3SUABQ6NezjKDDrnvC4yI5YZ3/huDjULLBvhed45xdCrzg=="
  "resolved" "https://registry.npmjs.org/@turf/rhumb-distance/-/rhumb-distance-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/sample@^6.5.0":
  "integrity" "sha512-kSdCwY7el15xQjnXYW520heKUrHwRvnzx8ka4eYxX9NFeOxaFITLW2G7UtXb6LJK8mmPXI8Aexv23F2ERqzGFg=="
  "resolved" "https://registry.npmjs.org/@turf/sample/-/sample-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/sector@^6.5.0":
  "integrity" "sha512-cYUOkgCTWqa23SOJBqxoFAc/yGCUsPRdn/ovbRTn1zNTm/Spmk6hVB84LCKOgHqvSF25i0d2kWqpZDzLDdAPbw=="
  "resolved" "https://registry.npmjs.org/@turf/sector/-/sector-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/circle" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-arc" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/shortest-path@^6.5.0":
  "integrity" "sha512-4de5+G7+P4hgSoPwn+SO9QSi9HY5NEV/xRJ+cmoFVRwv2CDsuOPDheHKeuIAhKyeKDvPvPt04XYWbac4insJMg=="
  "resolved" "https://registry.npmjs.org/@turf/shortest-path/-/shortest-path-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/bbox-polygon" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/clean-coords" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/transform-scale" "^6.5.0"

"@turf/simplify@^6.5.0":
  "integrity" "sha512-USas3QqffPHUY184dwQdP8qsvcVH/PWBYdXY5am7YTBACaQOMAlf6AKJs9FT8jiO6fQpxfgxuEtwmox+pBtlOg=="
  "resolved" "https://registry.npmjs.org/@turf/simplify/-/simplify-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/clean-coords" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/square-grid@^6.5.0":
  "integrity" "sha512-mlR0ayUdA+L4c9h7p4k3pX6gPWHNGuZkt2c5II1TJRmhLkW2557d6b/Vjfd1z9OVaajb1HinIs1FMSAPXuuUrA=="
  "resolved" "https://registry.npmjs.org/@turf/square-grid/-/square-grid-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/rectangle-grid" "^6.5.0"

"@turf/square@^6.5.0":
  "integrity" "sha512-BM2UyWDmiuHCadVhHXKIx5CQQbNCpOxB6S/aCNOCLbhCeypKX5Q0Aosc5YcmCJgkwO5BERCC6Ee7NMbNB2vHmQ=="
  "resolved" "https://registry.npmjs.org/@turf/square/-/square-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/standard-deviational-ellipse@^6.5.0":
  "integrity" "sha512-02CAlz8POvGPFK2BKK8uHGUk/LXb0MK459JVjKxLC2yJYieOBTqEbjP0qaWhiBhGzIxSMaqe8WxZ0KvqdnstHA=="
  "resolved" "https://registry.npmjs.org/@turf/standard-deviational-ellipse/-/standard-deviational-ellipse-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/center-mean" "^6.5.0"
    "@turf/ellipse" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/points-within-polygon" "^6.5.0"

"@turf/tag@^6.5.0":
  "integrity" "sha512-XwlBvrOV38CQsrNfrxvBaAPBQgXMljeU0DV8ExOyGM7/hvuGHJw3y8kKnQ4lmEQcmcrycjDQhP7JqoRv8vFssg=="
  "resolved" "https://registry.npmjs.org/@turf/tag/-/tag-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/tesselate@^6.5.0":
  "integrity" "sha512-M1HXuyZFCfEIIKkglh/r5L9H3c5QTEsnMBoZOFQiRnGPGmJWcaBissGb7mTFX2+DKE7FNWXh4TDnZlaLABB0dQ=="
  "resolved" "https://registry.npmjs.org/@turf/tesselate/-/tesselate-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "earcut" "^2.0.0"

"@turf/tin@^6.5.0":
  "integrity" "sha512-YLYikRzKisfwj7+F+Tmyy/LE3d2H7D4kajajIfc9mlik2+esG7IolsX/+oUz1biguDYsG0DUA8kVYXDkobukfg=="
  "resolved" "https://registry.npmjs.org/@turf/tin/-/tin-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/transform-rotate@^6.5.0":
  "integrity" "sha512-A2Ip1v4246ZmpssxpcL0hhiVBEf4L8lGnSPWTgSv5bWBEoya2fa/0SnFX9xJgP40rMP+ZzRaCN37vLHbv1Guag=="
  "resolved" "https://registry.npmjs.org/@turf/transform-rotate/-/transform-rotate-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/centroid" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/rhumb-bearing" "^6.5.0"
    "@turf/rhumb-destination" "^6.5.0"
    "@turf/rhumb-distance" "^6.5.0"

"@turf/transform-scale@^6.5.0":
  "integrity" "sha512-VsATGXC9rYM8qTjbQJ/P7BswKWXHdnSJ35JlV4OsZyHBMxJQHftvmZJsFbOqVtQnIQIzf2OAly6rfzVV9QLr7g=="
  "resolved" "https://registry.npmjs.org/@turf/transform-scale/-/transform-scale-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/center" "^6.5.0"
    "@turf/centroid" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/rhumb-bearing" "^6.5.0"
    "@turf/rhumb-destination" "^6.5.0"
    "@turf/rhumb-distance" "^6.5.0"

"@turf/transform-translate@^6.5.0":
  "integrity" "sha512-NABLw5VdtJt/9vSstChp93pc6oel4qXEos56RBMsPlYB8hzNTEKYtC146XJvyF4twJeeYS8RVe1u7KhoFwEM5w=="
  "resolved" "https://registry.npmjs.org/@turf/transform-translate/-/transform-translate-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/rhumb-destination" "^6.5.0"

"@turf/triangle-grid@^6.5.0":
  "integrity" "sha512-2jToUSAS1R1htq4TyLQYPTIsoy6wg3e3BQXjm2rANzw4wPQCXGOxrur1Fy9RtzwqwljlC7DF4tg0OnWr8RjmfA=="
  "resolved" "https://registry.npmjs.org/@turf/triangle-grid/-/triangle-grid-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/intersect" "^6.5.0"

"@turf/truncate@^6.5.0":
  "integrity" "sha512-pFxg71pLk+eJj134Z9yUoRhIi8vqnnKvCYwdT4x/DQl/19RVdq1tV3yqOT3gcTQNfniteylL5qV1uTBDV5sgrg=="
  "resolved" "https://registry.npmjs.org/@turf/truncate/-/truncate-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/turf@^6.5.0":
  "integrity" "sha512-ipMCPnhu59bh92MNt8+pr1VZQhHVuTMHklciQURo54heoxRzt1neNYZOBR6jdL+hNsbDGAECMuIpAutX+a3Y+w=="
  "resolved" "https://registry.npmjs.org/@turf/turf/-/turf-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/along" "^6.5.0"
    "@turf/angle" "^6.5.0"
    "@turf/area" "^6.5.0"
    "@turf/bbox" "^6.5.0"
    "@turf/bbox-clip" "^6.5.0"
    "@turf/bbox-polygon" "^6.5.0"
    "@turf/bearing" "^6.5.0"
    "@turf/bezier-spline" "^6.5.0"
    "@turf/boolean-clockwise" "^6.5.0"
    "@turf/boolean-contains" "^6.5.0"
    "@turf/boolean-crosses" "^6.5.0"
    "@turf/boolean-disjoint" "^6.5.0"
    "@turf/boolean-equal" "^6.5.0"
    "@turf/boolean-intersects" "^6.5.0"
    "@turf/boolean-overlap" "^6.5.0"
    "@turf/boolean-parallel" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/boolean-point-on-line" "^6.5.0"
    "@turf/boolean-within" "^6.5.0"
    "@turf/buffer" "^6.5.0"
    "@turf/center" "^6.5.0"
    "@turf/center-mean" "^6.5.0"
    "@turf/center-median" "^6.5.0"
    "@turf/center-of-mass" "^6.5.0"
    "@turf/centroid" "^6.5.0"
    "@turf/circle" "^6.5.0"
    "@turf/clean-coords" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/clusters" "^6.5.0"
    "@turf/clusters-dbscan" "^6.5.0"
    "@turf/clusters-kmeans" "^6.5.0"
    "@turf/collect" "^6.5.0"
    "@turf/combine" "^6.5.0"
    "@turf/concave" "^6.5.0"
    "@turf/convex" "^6.5.0"
    "@turf/destination" "^6.5.0"
    "@turf/difference" "^6.5.0"
    "@turf/dissolve" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/distance-weight" "^6.5.0"
    "@turf/ellipse" "^6.5.0"
    "@turf/envelope" "^6.5.0"
    "@turf/explode" "^6.5.0"
    "@turf/flatten" "^6.5.0"
    "@turf/flip" "^6.5.0"
    "@turf/great-circle" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/hex-grid" "^6.5.0"
    "@turf/interpolate" "^6.5.0"
    "@turf/intersect" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/isobands" "^6.5.0"
    "@turf/isolines" "^6.5.0"
    "@turf/kinks" "^6.5.0"
    "@turf/length" "^6.5.0"
    "@turf/line-arc" "^6.5.0"
    "@turf/line-chunk" "^6.5.0"
    "@turf/line-intersect" "^6.5.0"
    "@turf/line-offset" "^6.5.0"
    "@turf/line-overlap" "^6.5.0"
    "@turf/line-segment" "^6.5.0"
    "@turf/line-slice" "^6.5.0"
    "@turf/line-slice-along" "^6.5.0"
    "@turf/line-split" "^6.5.0"
    "@turf/line-to-polygon" "^6.5.0"
    "@turf/mask" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/midpoint" "^6.5.0"
    "@turf/moran-index" "^6.5.0"
    "@turf/nearest-point" "^6.5.0"
    "@turf/nearest-point-on-line" "^6.5.0"
    "@turf/nearest-point-to-line" "^6.5.0"
    "@turf/planepoint" "^6.5.0"
    "@turf/point-grid" "^6.5.0"
    "@turf/point-on-feature" "^6.5.0"
    "@turf/point-to-line-distance" "^6.5.0"
    "@turf/points-within-polygon" "^6.5.0"
    "@turf/polygon-smooth" "^6.5.0"
    "@turf/polygon-tangents" "^6.5.0"
    "@turf/polygon-to-line" "^6.5.0"
    "@turf/polygonize" "^6.5.0"
    "@turf/projection" "^6.5.0"
    "@turf/random" "^6.5.0"
    "@turf/rewind" "^6.5.0"
    "@turf/rhumb-bearing" "^6.5.0"
    "@turf/rhumb-destination" "^6.5.0"
    "@turf/rhumb-distance" "^6.5.0"
    "@turf/sample" "^6.5.0"
    "@turf/sector" "^6.5.0"
    "@turf/shortest-path" "^6.5.0"
    "@turf/simplify" "^6.5.0"
    "@turf/square" "^6.5.0"
    "@turf/square-grid" "^6.5.0"
    "@turf/standard-deviational-ellipse" "^6.5.0"
    "@turf/tag" "^6.5.0"
    "@turf/tesselate" "^6.5.0"
    "@turf/tin" "^6.5.0"
    "@turf/transform-rotate" "^6.5.0"
    "@turf/transform-scale" "^6.5.0"
    "@turf/transform-translate" "^6.5.0"
    "@turf/triangle-grid" "^6.5.0"
    "@turf/truncate" "^6.5.0"
    "@turf/union" "^6.5.0"
    "@turf/unkink-polygon" "^6.5.0"
    "@turf/voronoi" "^6.5.0"

"@turf/union@^6.5.0":
  "integrity" "sha512-igYWCwP/f0RFHIlC2c0SKDuM/ObBaqSljI3IdV/x71805QbIvY/BYGcJdyNcgEA6cylIGl/0VSlIbpJHZ9ldhw=="
  "resolved" "https://registry.npmjs.org/@turf/union/-/union-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "polygon-clipping" "^0.15.3"

"@turf/unkink-polygon@^6.5.0":
  "integrity" "sha512-8QswkzC0UqKmN1DT6HpA9upfa1HdAA5n6bbuzHy8NJOX8oVizVAqfEPY0wqqTgboDjmBR4yyImsdPGUl3gZ8JQ=="
  "resolved" "https://registry.npmjs.org/@turf/unkink-polygon/-/unkink-polygon-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/area" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "rbush" "^2.0.1"

"@turf/voronoi@^6.5.0":
  "integrity" "sha512-C/xUsywYX+7h1UyNqnydHXiun4UPjK88VDghtoRypR9cLlb7qozkiLRphQxxsCM0KxyxpVPHBVQXdAL3+Yurow=="
  "resolved" "https://registry.npmjs.org/@turf/voronoi/-/voronoi-6.5.0.tgz"
  "version" "6.5.0"
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "d3-voronoi" "1.1.2"

"@types/d3-timer@^2.0.0":
  "integrity" "sha512-TF8aoF5cHcLO7W7403blM7L1T+6NF3XMyN3fxyUolq2uOcFeicG/khQg/dGxiCJWoAcmYulYN7LYSRKO54IXaA=="
  "resolved" "https://registry.npmjs.org/@types/d3-timer/-/d3-timer-2.0.1.tgz"
  "version" "2.0.1"

"@types/eslint-scope@^3.7.7":
  "integrity" "sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg=="
  "resolved" "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.7.tgz"
  "version" "3.7.7"
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  "integrity" "sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag=="
  "resolved" "https://registry.npmjs.org/@types/eslint/-/eslint-9.6.1.tgz"
  "version" "9.6.1"
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.6":
  "integrity" "sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw=="
  "resolved" "https://registry.npmjs.org/@types/estree/-/estree-1.0.6.tgz"
  "version" "1.0.6"

"@types/geojson@7946.0.8":
  "integrity" "sha512-1rkryxURpr6aWP7R786/UQOkJ3PcpQiWkAXBmdWc7ryFWqN6a4xfK7BtjXvFBKO9LjQ+MWQSWxYeZX1OApnArA=="
  "resolved" "https://registry.npmjs.org/@types/geojson/-/geojson-7946.0.8.tgz"
  "version" "7946.0.8"

"@types/hoist-non-react-statics@^3.3.0":
  "integrity" "sha512-iMIqiko6ooLrTh1joXodJK5X9xeEALT1kM5G3ZLhD3hszxBdIEd5C75U834D9mLcINgD4OyZf5uQXjkuYydWvA=="
  "resolved" "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "@types/react" "*"
    "hoist-non-react-statics" "^3.3.0"

"@types/json-schema@*", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  "integrity" "sha512-qcUXuemtEu+E5wZSJHNxUXeCZhAfXKQ41D+duX+VYPde7xyEVZci+/oXKJL13tnRs9lR2pr4fod59GT6/X1/yQ=="
  "resolved" "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.9.tgz"
  "version" "7.0.9"

"@types/minimist@^1.2.0":
  "integrity" "sha512-jhuKLIRrhvCPLqwPcx6INqmKeiA5EWrsCOPhrlFSrbrmU4ZMPjj5Ul/oLCMDO98XRUIwVm78xICz4EPCektzeQ=="
  "resolved" "https://registry.npmjs.org/@types/minimist/-/minimist-1.2.2.tgz"
  "version" "1.2.2"

"@types/node@*", "@types/node@^16.11.10":
  "integrity" "sha512-Pf8M1XD9i1ksZEcCP8vuSNwooJ/bZapNmIzpmsMaL+jMI+8mEYU3PKvs+xDNuQcJWF/x24WzY4qxLtB0zNow9A=="
  "resolved" "https://registry.npmjs.org/@types/node/-/node-16.11.21.tgz"
  "version" "16.11.21"

"@types/normalize-package-data@^2.4.0":
  "integrity" "sha512-Gj7cI7z+98M282Tqmp2K5EIsoouUEzbBJhQQzDE3jSIRk6r9gsz0oUokqIUR4u1R3dMHo0pDHM7sNOHyhulypw=="
  "resolved" "https://registry.npmjs.org/@types/normalize-package-data/-/normalize-package-data-2.4.1.tgz"
  "version" "2.4.1"

"@types/parse-json@^4.0.0":
  "integrity" "sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA=="
  "resolved" "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.0.tgz"
  "version" "4.0.0"

"@types/prop-types@*":
  "integrity" "sha512-rZ5drC/jWjrArrS8BR6SIr4cWpW09RNTYt9AMZo3Jwwif+iacXAqgVjm0B0Bv/S1jhDXKHqRVNCbACkJ89RAnQ=="
  "resolved" "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.4.tgz"
  "version" "15.7.4"

"@types/q@^1.5.1":
  "integrity" "sha512-L28j2FcJfSZOnL1WBjDYp2vUHCeIFlyYI/53EwD/rKUBQ7MtUUfbQWiyKJGpcnv4/WgrhWsFKrcPstcAt/J0tQ=="
  "resolved" "https://registry.npmjs.org/@types/q/-/q-1.5.5.tgz"
  "version" "1.5.5"

"@types/react-dom@^17.0.0":
  "integrity" "sha512-f96K3k+24RaLGVu/Y2Ng3e1EbZ8/cVJvypZWd7cy0ofCBaf2lcM46xNhycMZ2xGwbBjRql7hOlZ+e2WlJ5MH3Q=="
  "resolved" "https://registry.npmjs.org/@types/react-dom/-/react-dom-17.0.11.tgz"
  "version" "17.0.11"
  dependencies:
    "@types/react" "*"

"@types/react-redux@^7.1.20":
  "integrity" "sha512-q42es4c8iIeTgcnB+yJgRTTzftv3eYYvCZOh1Ckn2eX/3o5TdsQYKUWpLoLuGlcY/p+VAhV9IOEZJcWk/vfkXw=="
  "resolved" "https://registry.npmjs.org/@types/react-redux/-/react-redux-7.1.20.tgz"
  "version" "7.1.20"
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.0"
    "@types/react" "*"
    "hoist-non-react-statics" "^3.3.0"
    "redux" "^4.0.0"

"@types/react@*", "@types/react@^17.0.0":
  "integrity" "sha512-2FS1oTqBGcH/s0E+CjrCCR9+JMpsu9b69RTFO+40ua43ZqP5MmQ4iUde/dMjWR909KxZwmOQIFq6AV6NjEG5xg=="
  "resolved" "https://registry.npmjs.org/@types/react/-/react-17.0.37.tgz"
  "version" "17.0.37"
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    "csstype" "^3.0.2"

"@types/scheduler@*":
  "integrity" "sha512-hppQEBDmlwhFAXKJX2KnWLYu5yMfi91yazPb2l+lbJiwW+wdo1gNeRA+3RgNSO39WYX2euey41KEwnqesU2Jew=="
  "resolved" "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.2.tgz"
  "version" "0.16.2"

"@typescript-eslint/eslint-plugin@^5.4.0":
  "integrity" "sha512-4bV6fulqbuaO9UMXU0Ia0o6z6if+kmMRW8rMRyfqXj/eGrZZRGedS4n0adeGNnjr8LKAM495hrQ7Tea52UWmQA=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@typescript-eslint/experimental-utils" "5.5.0"
    "@typescript-eslint/scope-manager" "5.5.0"
    "debug" "^4.3.2"
    "functional-red-black-tree" "^1.0.1"
    "ignore" "^5.1.8"
    "regexpp" "^3.2.0"
    "semver" "^7.3.5"
    "tsutils" "^3.21.0"

"@typescript-eslint/experimental-utils@5.5.0":
  "integrity" "sha512-kjWeeVU+4lQ1SLYErRKV5yDXbWDPkpbzTUUlfAUifPYvpX0qZlrcCZ96/6oWxt3QxtK5WVhXz+KsnwW9cIW+3A=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/experimental-utils/-/experimental-utils-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@types/json-schema" "^7.0.9"
    "@typescript-eslint/scope-manager" "5.5.0"
    "@typescript-eslint/types" "5.5.0"
    "@typescript-eslint/typescript-estree" "5.5.0"
    "eslint-scope" "^5.1.1"
    "eslint-utils" "^3.0.0"

"@typescript-eslint/parser@^5.0.0", "@typescript-eslint/parser@^5.4.0":
  "integrity" "sha512-JsXBU+kgQOAgzUn2jPrLA+Rd0Y1dswOlX3hp8MuRO1hQDs6xgHtbCXEiAu7bz5hyVURxbXcA2draasMbNqrhmg=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@typescript-eslint/scope-manager" "5.5.0"
    "@typescript-eslint/types" "5.5.0"
    "@typescript-eslint/typescript-estree" "5.5.0"
    "debug" "^4.3.2"

"@typescript-eslint/scope-manager@5.5.0":
  "integrity" "sha512-0/r656RmRLo7CbN4Mdd+xZyPJ/fPCKhYdU6mnZx+8msAD8nJSP8EyCFkzbd6vNVZzZvWlMYrSNekqGrCBqFQhg=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@typescript-eslint/types" "5.5.0"
    "@typescript-eslint/visitor-keys" "5.5.0"

"@typescript-eslint/types@5.5.0":
  "integrity" "sha512-OaYTqkW3GnuHxqsxxJ6KypIKd5Uw7bFiQJZRyNi1jbMJnK3Hc/DR4KwB6KJj6PBRkJJoaNwzMNv9vtTk87JhOg=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/types/-/types-5.5.0.tgz"
  "version" "5.5.0"

"@typescript-eslint/typescript-estree@5.5.0":
  "integrity" "sha512-pVn8btYUiYrjonhMAO0yG8lm7RApzy2L4RC7Td/mC/qFkyf6vRbGyZozoA94+w6D2Y2GRqpMoCWcwx/EUOzyoQ=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@typescript-eslint/types" "5.5.0"
    "@typescript-eslint/visitor-keys" "5.5.0"
    "debug" "^4.3.2"
    "globby" "^11.0.4"
    "is-glob" "^4.0.3"
    "semver" "^7.3.5"
    "tsutils" "^3.21.0"

"@typescript-eslint/visitor-keys@5.5.0":
  "integrity" "sha512-4GzJ1kRtsWzHhdM40tv0ZKHNSbkDhF0Woi/TDwVJX6UICwJItvP7ZTXbjTkCdrors7ww0sYe0t+cIKDAJwZ7Kw=="
  "resolved" "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@typescript-eslint/types" "5.5.0"
    "eslint-visitor-keys" "^3.0.0"

"@vitejs/plugin-react@^1.3.2":
  "integrity" "sha512-aurBNmMo0kz1O4qRoY+FM4epSA39y3ShWGuqfLRA/3z0oEJAdtoSfgA3aO98/PCCHAqMaduLxIxErWrVKIFzXA=="
  "resolved" "https://registry.npmmirror.com/@vitejs/plugin-react/-/plugin-react-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "@babel/core" "^7.17.10"
    "@babel/plugin-transform-react-jsx" "^7.17.3"
    "@babel/plugin-transform-react-jsx-development" "^7.16.7"
    "@babel/plugin-transform-react-jsx-self" "^7.16.7"
    "@babel/plugin-transform-react-jsx-source" "^7.16.7"
    "@rollup/pluginutils" "^4.2.1"
    "react-refresh" "^0.13.0"
    "resolve" "^1.22.0"

"@webassemblyjs/ast@^1.14.1", "@webassemblyjs/ast@1.14.1":
  "integrity" "sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/helper-numbers" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"

"@webassemblyjs/floating-point-hex-parser@1.13.2":
  "integrity" "sha512-6oXyTOzbKxGH4steLbLNOu71Oj+C8Lg34n6CqRvqfS2O71BxY6ByfMDRhBytzknj9yGUPVJ1qIKhRlAwO1AovA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.13.2.tgz"
  "version" "1.13.2"

"@webassemblyjs/helper-api-error@1.13.2":
  "integrity" "sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.2.tgz"
  "version" "1.13.2"

"@webassemblyjs/helper-buffer@1.14.1":
  "integrity" "sha512-jyH7wtcHiKssDtFPRB+iQdxlDf96m0E39yb0k5uJVhFGleZFoNw1c4aeIcVUPPbXUVJ94wwnMOAqUHyzoEPVMA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.14.1.tgz"
  "version" "1.14.1"

"@webassemblyjs/helper-numbers@1.13.2":
  "integrity" "sha512-FE8aCmS5Q6eQYcV3gI35O4J789wlQA+7JrqTTpJqn5emA4U2hvwJmvFRC0HODS+3Ye6WioDklgd6scJ3+PLnEA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.13.2.tgz"
  "version" "1.13.2"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.13.2"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.13.2":
  "integrity" "sha512-3QbLKy93F0EAIXLh0ogEVR6rOubA9AoZ+WRYhNbFyuB70j3dRdwH9g+qXhLAO0kiYGlg3TxDV+I4rQTr/YNXkA=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.13.2.tgz"
  "version" "1.13.2"

"@webassemblyjs/helper-wasm-section@1.14.1":
  "integrity" "sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/wasm-gen" "1.14.1"

"@webassemblyjs/ieee754@1.13.2":
  "integrity" "sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.13.2.tgz"
  "version" "1.13.2"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.13.2":
  "integrity" "sha512-Lde1oNoIdzVzdkNEAWZ1dZ5orIbff80YPdHx20mrHwHrVNNTjNr8E3xz9BdpcGqRQbAEa+fkrCb+fRFTl/6sQw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.13.2.tgz"
  "version" "1.13.2"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.13.2":
  "integrity" "sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.13.2.tgz"
  "version" "1.13.2"

"@webassemblyjs/wasm-edit@^1.14.1":
  "integrity" "sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/helper-wasm-section" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-opt" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"
    "@webassemblyjs/wast-printer" "1.14.1"

"@webassemblyjs/wasm-gen@1.14.1":
  "integrity" "sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wasm-opt@1.14.1":
  "integrity" "sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"

"@webassemblyjs/wasm-parser@^1.14.1", "@webassemblyjs/wasm-parser@1.14.1":
  "integrity" "sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wast-printer@1.14.1":
  "integrity" "sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw=="
  "resolved" "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  "integrity" "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA=="
  "resolved" "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.2":
  "integrity" "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ=="
  "resolved" "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz"
  "version" "4.2.2"

"abs-svg-path@~0.1.1":
  "integrity" "sha1-32Acjo0roQ1KdtYl4japo5wnI78= sha512-d8XPSGjfyzlXC3Xx891DJRyZfqk5JU0BJrDQcsWomFIV1/BIzPW5HDH5iDdWpqWaav0YVIEzT1RHTwWr0FFshA=="
  "resolved" "https://registry.npmjs.org/abs-svg-path/-/abs-svg-path-0.1.1.tgz"
  "version" "0.1.1"

"acorn-jsx@^5.3.1":
  "integrity" "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ=="
  "resolved" "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^8.14.0", "acorn@^8.7.0", "acorn@^8.8.2":
  "integrity" "sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-8.14.0.tgz"
  "version" "8.14.0"

"ajv-formats@^2.1.1":
  "integrity" "sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA=="
  "resolved" "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "ajv" "^8.0.0"

"ajv-keywords@^3.5.2":
  "integrity" "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ=="
  "resolved" "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  "version" "3.5.2"

"ajv-keywords@^5.1.0":
  "integrity" "sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw=="
  "resolved" "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "fast-deep-equal" "^3.1.3"

"ajv@^6.10.0", "ajv@^6.12.4", "ajv@^6.12.5", "ajv@^6.9.1":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^8.0.0":
  "integrity" "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz"
  "version" "8.17.1"
  dependencies:
    "fast-deep-equal" "^3.1.3"
    "fast-uri" "^3.0.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"

"ajv@^8.0.1":
  "integrity" "sha512-x9VuX+R/jcFj1DHo/fCp99esgGDWiHENrKxaCENuCxpoMCmAt/COCGVDwA7kleEpEzJjDnvh3yGoOuLu0Dtllw=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.8.2.tgz"
  "version" "8.8.2"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"ajv@^8.8.2", "ajv@^8.9.0":
  "integrity" "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz"
  "version" "8.17.1"
  dependencies:
    "fast-deep-equal" "^3.1.3"
    "fast-uri" "^3.0.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"

"align-text@^0.1.1", "align-text@^0.1.3":
  "integrity" "sha1-DNkKVhCT810KmSVsIrcGlDP60Rc= sha512-GrTZLRpmp6wIC2ztrWW9MjjTgSKccffgFagbNDOX95/dcjEcYZibYTeaOntySQLcdw1ztBoFkviiUvTMbb9MYg=="
  "resolved" "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"
    "longest" "^1.0.1"
    "repeat-string" "^1.5.2"

"amdefine@>=0.0.4":
  "integrity" "sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU= sha512-S2Hw0TtNkMJhIabBwIojKL9YHO5T0n5eNqWJ7Lrlel/zDbftQpxpapi8tZs3X1HWa+u+QeydGmzzNU0m09+Rcg=="
  "resolved" "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz"
  "version" "1.0.1"

"ansi-regex@^2.0.0":
  "integrity" "sha1-w7M6te42DYbg5ijwRorn7yfWVN8= sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@^5.0.1":
  "integrity" "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-styles@^2.2.1":
  "integrity" "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4= sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz"
  "version" "2.2.1"

"ansi-styles@^3.2.1":
  "integrity" "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0", "ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"arco-design-pro@^2.8.1":
  "integrity" "sha512-QhmobhyfkqaqOBSLHdkba2F3EtDxhWOIUh9xNBuds3Qnwy4dBWY70rFxePCHZHmt6Xffmh+nbdOdlYw4xoWeug=="
  "resolved" "https://registry.npmmirror.com/arco-design-pro/-/arco-design-pro-2.8.1.tgz"
  "version" "2.8.1"
  dependencies:
    "fs-extra" "^10.0.0"
    "minimist" "^1.2.5"

"argparse@^1.0.7":
  "integrity" "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"argparse@^2.0.1":
  "integrity" "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  "version" "2.0.1"

"array-includes@^3.1.3", "array-includes@^3.1.4":
  "integrity" "sha512-ZTNSQkmWumEbiHO2GF4GmWxYVTiQyJy2XOTa15sdQSrvKn7l+180egQMqlrMOUMCyLMD7pmyQe4mMDUT6Behrw=="
  "resolved" "https://registry.npmjs.org/array-includes/-/array-includes-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"
    "get-intrinsic" "^1.1.1"
    "is-string" "^1.0.7"

"array-union@^2.1.0":
  "integrity" "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw=="
  "resolved" "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz"
  "version" "2.1.0"

"array.prototype.flatmap@^1.2.5":
  "integrity" "sha512-08u6rVyi1Lj7oqWbS9nUxliETrtIROT4XGTA4D/LWGten6E3ocm7cy9SIrmNHOL5XVbVuckUp3X6Xyg8/zpvHA=="
  "resolved" "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.2.5.tgz"
  "version" "1.2.5"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.0"

"arrify@^1.0.1":
  "integrity" "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0= sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA=="
  "resolved" "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz"
  "version" "1.0.1"

"astral-regex@^2.0.0":
  "integrity" "sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ=="
  "resolved" "https://registry.npmjs.org/astral-regex/-/astral-regex-2.0.0.tgz"
  "version" "2.0.0"

"axios@^0.24.0":
  "integrity" "sha512-Q6cWsys88HoPgAaFAVUb0WpPk0O8iTeisR9IMqy9G8AbO4NlpVknrnQS03zzF9PGAWgO3cgletO3VjV/P7VztA=="
  "resolved" "https://registry.npmjs.org/axios/-/axios-0.24.0.tgz"
  "version" "0.24.0"
  dependencies:
    "follow-redirects" "^1.14.4"

"b-tween@^0.3.3":
  "integrity" "sha512-oEHegcRpA7fAuc9KC4nktucuZn2aS8htymCPcP3qkEGPqiBH+GfqtqoG2l7LxHngg6O0HFM7hOeOYExl1Oz4ZA=="
  "resolved" "https://registry.npmjs.org/b-tween/-/b-tween-0.3.3.tgz"
  "version" "0.3.3"

"b-validate@^1.4.2":
  "integrity" "sha512-iCvCkGFskbaYtfQ0a3GmcQCHl/Sv1GufXFGuUQ+FE+WJa7A/espLOuFIn09B944V8/ImPj71T4+rTASxO2PAuA=="
  "resolved" "https://registry.npmmirror.com/b-validate/-/b-validate-1.5.3.tgz"
  "version" "1.5.3"

"babel-plugin-dynamic-import-node@^2.3.3":
  "integrity" "sha512-jZVI+s9Zg3IqA/kdi0i6UDCybUI3aSBLnglhYbSSjKlV7yF1F/5LWv8MakQmvYpnbJDS6fcBL2KzHSxNCMtWSQ=="
  "resolved" "https://registry.npmjs.org/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "object.assign" "^4.1.0"

"babel-plugin-import@^1.0.0":
  "integrity" "sha512-1qCWdljJOrDRH/ybaCZuDgySii4yYrtQ8OJQwrcDqdt0y67N30ng3X3nABg6j7gR7qUJgcMa9OMhc4AGViDwWw=="
  "resolved" "https://registry.npmjs.org/babel-plugin-import/-/babel-plugin-import-1.13.3.tgz"
  "version" "1.13.3"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/runtime" "^7.0.0"

"babel-plugin-polyfill-corejs2@^0.3.0":
  "integrity" "sha512-wMDoBJ6uG4u4PNFh72Ty6t3EgfA91puCuAwKIazbQlci+ENb/UU9A3xG5lutjUIiXCIn1CY5L15r9LimiJyrSA=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "@babel/compat-data" "^7.13.11"
    "@babel/helper-define-polyfill-provider" "^0.3.0"
    "semver" "^6.1.1"

"babel-plugin-polyfill-corejs3@^0.4.0":
  "integrity" "sha512-YxFreYwUfglYKdLUGvIF2nJEsGwj+RhWSX/ije3D2vQPOXuyMLMtg/cCGMDpOA7Nd+MwlNdnGODbd2EwUZPlsw=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.0"
    "core-js-compat" "^3.18.0"

"babel-plugin-polyfill-regenerator@^0.3.0":
  "integrity" "sha512-dhAPTDLGoMW5/84wkgwiLRwMnio2i1fUe53EuvtKMv0pn2p3S8OCoV1xAzfJPl0KOX7IB89s2ib85vbYiea3jg=="
  "resolved" "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.0"

"babel-plugin-transform-replace-object-assign@^2.0.0":
  "integrity" "sha512-PMT+dRz6JAHbXIsJB4XjcIstmKK9SFj9MYZGcEWW/1jISiemGz9w6TVLrj4hgpR89X0J9mFuHq61zPvP8lgZZQ=="
  "resolved" "https://registry.npmjs.org/babel-plugin-transform-replace-object-assign/-/babel-plugin-transform-replace-object-assign-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"balanced-match@^2.0.0":
  "integrity" "sha512-1ugUSr8BHXRnK23KfuYS+gVMC3LB8QGH9W1iGtDPsNWoQbgtXSExkBu2aDR4epiGWZOjZsj6lDl/N/AqqTC3UA=="
  "resolved" "https://registry.npmjs.org/balanced-match/-/balanced-match-2.0.0.tgz"
  "version" "2.0.0"

"big.js@^5.2.2":
  "integrity" "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="
  "resolved" "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz"
  "version" "5.2.2"

"bizcharts@^4.1.11":
  "integrity" "sha512-CKwRcm4QfgrdoldSOvVPkCCGE5IrHN1S3KSBEfGk9jXme5POH3S3RQAQVr2+IxMb6NugrdV0zesivPfSSReL+w=="
  "resolved" "https://registry.npmjs.org/bizcharts/-/bizcharts-4.1.14.tgz"
  "version" "4.1.14"
  dependencies:
    "@antv/component" "*"
    "@antv/g2" "4.1.30"
    "@antv/g2plot" "2.3.35"
    "@antv/util" "*"
    "@babel/plugin-transform-modules-commonjs" "^7.12.1"
    "@babel/plugin-transform-runtime" "^7.12.10"
    "@juggle/resize-observer" "^3.3.1"
    "babel-plugin-transform-replace-object-assign" "^2.0.0"
    "d3-color" "^1.4.1"
    "react-error-boundary" "3.0.2"
    "react-reconciler" "^0.25.1"
    "simple-statistics" "^7.1.0"
    "warning" "^4.0.3"

"boolbase@^1.0.0", "boolbase@~1.0.0":
  "integrity" "sha1-aN/1++YMUes3cl6p4+0xDcwed24= sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww=="
  "resolved" "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^3.0.1":
  "integrity" "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"browserslist@^4.18.1", "browserslist@^4.24.0", "browserslist@>= 4.21.0":
  "integrity" "sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A=="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz"
  "version" "4.24.4"
  dependencies:
    "caniuse-lite" "^1.0.30001688"
    "electron-to-chromium" "^1.5.73"
    "node-releases" "^2.0.19"
    "update-browserslist-db" "^1.1.1"

"buffer-from@^1.0.0":
  "integrity" "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="
  "resolved" "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
  "version" "1.1.2"

"call-bind@^1.0.0", "call-bind@^1.0.2", "call-bind@~1.0.2":
  "integrity" "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA=="
  "resolved" "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.0.2"

"callsites@^3.0.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camelcase-keys@^6.2.2":
  "integrity" "sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg=="
  "resolved" "https://registry.npmjs.org/camelcase-keys/-/camelcase-keys-6.2.2.tgz"
  "version" "6.2.2"
  dependencies:
    "camelcase" "^5.3.1"
    "map-obj" "^4.0.0"
    "quick-lru" "^4.0.1"

"camelcase@^1.0.2":
  "integrity" "sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk= sha512-wzLkDa4K/mzI1OSITC+DUyjgIl/ETNHE9QvYgy6J6Jvqyyz4C0Xfd+lQhb19sX2jMpZV4IssUn0VDVmglV+s4g=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz"
  "version" "1.2.1"

"camelcase@^5.3.1":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^6.2.0":
  "integrity" "sha512-tVI4q5jjFV5CavAU8DXfza/TJcZutVKo/5Foskmsqcm0MsL91moHvwiGNnqaa2o6PF/7yT5ikDRcVcl8Rj6LCA=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-6.2.1.tgz"
  "version" "6.2.1"

"caniuse-lite@^1.0.30001688":
  "integrity" "sha512-b+uH5BakXZ9Do9iK+CkDmctUSEqZl+SP056vc5usa0PL+ev5OHw003rZXcnjNDv3L8P5j6rwT6C0BPKSikW08w=="
  "resolved" "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001699.tgz"
  "version" "1.0.30001699"

"center-align@^0.1.1":
  "integrity" "sha1-qg0yYptu6XIgBBHL1EYckHvCt60= sha512-Baz3aNe2gd2LP2qk5U+sDk/m4oSuwSDcBfayTCTBoWpfIGO5XFxPmjILQII4NGiZjD6DoDI6kf7gKaxkf7s3VQ=="
  "resolved" "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "align-text" "^0.1.3"
    "lazy-cache" "^1.0.3"

"chalk@^1.1.1":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg= sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^2.4.1":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^4.0.0", "chalk@^4.1.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chrome-trace-event@^1.0.2":
  "integrity" "sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ=="
  "resolved" "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz"
  "version" "1.0.4"

"classnames@^2.3.1":
  "integrity" "sha512-OlQdbZ7gLfGarSqxesMesDa5uz7KFbID8Kpq/SxIoNGDqY8lSYs0D+hhtBXhcdB3rcbXArFr7vlHheLk1voeNA=="
  "resolved" "https://registry.npmjs.org/classnames/-/classnames-2.3.1.tgz"
  "version" "2.3.1"

"cliui@^2.1.0":
  "integrity" "sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE= sha512-GIOYRizG+TGoc7Wgc1LiOTLare95R3mzKgoln+Q/lE4ceiYH19gUpl0l0Ffq4lJDEf3FxujMe6IBfOCs7pfqNA=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "center-align" "^0.1.1"
    "right-align" "^0.1.1"
    "wordwrap" "0.0.2"

"clone-regexp@^2.1.0":
  "integrity" "sha512-beMpP7BOtTipFuW8hrJvREQ2DrRu3BE7by0ZpibtfBA+qfHYvMGTc2Yb1JMYPKg/JUw0CHYvpg796aNTSW9z7Q=="
  "resolved" "https://registry.npmjs.org/clone-regexp/-/clone-regexp-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "is-regexp" "^2.0.0"

"coa@^2.0.2":
  "integrity" "sha512-q5/jG+YQnSy4nRTV4F7lPepBJZ8qBNJJDBuJdoejDyLXgmL7IEo+Le2JDZudFTFt7mrCqIRaSjws4ygRCTCAXA=="
  "resolved" "https://registry.npmjs.org/coa/-/coa-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "@types/q" "^1.5.1"
    "chalk" "^2.4.1"
    "q" "^1.1.2"

"color-convert@^1.9.0":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^1.9.3":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@^1.0.0", "color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU= sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-string@^1.6.0":
  "integrity" "sha512-AGfGNQbnXlYqPStIx3QB2XA3Wy8vjbreqklmCiGVwcoHSLN5KIpDZDflYnXlBliKHI8CTBX3PsCgG+xfZgqK8A=="
  "resolved" "https://registry.npmjs.org/color-string/-/color-string-1.8.1.tgz"
  "version" "1.8.1"
  dependencies:
    "color-name" "^1.0.0"
    "simple-swizzle" "^0.2.2"

"color@^3.1.3":
  "integrity" "sha512-aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA=="
  "resolved" "https://registry.npmjs.org/color/-/color-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.3"
    "color-string" "^1.6.0"

"commander@*", "commander@^2.20.0", "commander@2":
  "integrity" "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="
  "resolved" "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"compute-scroll-into-view@^1.0.17", "compute-scroll-into-view@^1.0.20":
  "integrity" "sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg=="
  "resolved" "https://registry.npmmirror.com/compute-scroll-into-view/-/compute-scroll-into-view-1.0.20.tgz"
  "version" "1.0.20"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s= sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="
  "resolved" "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concaveman@*":
  "integrity" "sha512-PwZYKaM/ckQSa8peP5JpVr7IMJ4Nn/MHIaWUjP4be+KoZ7Botgs8seAZGpmaOM+UZXawcdYRao/px9ycrCihHw=="
  "resolved" "https://registry.npmjs.org/concaveman/-/concaveman-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "point-in-polygon" "^1.1.0"
    "rbush" "^3.0.1"
    "robust-predicates" "^2.0.4"
    "tinyqueue" "^2.0.3"

"contour_plot@^0.0.1":
  "integrity" "sha1-R1hw8DK44zhBKqX8UHiA8L9JXHc= sha512-Nil2HI76Xux6sVGORvhSS8v66m+/h5CwFkBJDO+U5vWaMdNC0yXNCsGDPbzPhvqOEU5koebhdEvD372LI+IyLw=="
  "resolved" "https://registry.npmjs.org/contour_plot/-/contour_plot-0.0.1.tgz"
  "version" "0.0.1"

"convert-source-map@^2.0.0":
  "integrity" "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg=="
  "resolved" "https://registry.npmmirror.com/convert-source-map/-/convert-source-map-2.0.0.tgz"
  "version" "2.0.0"

"copy-anything@^2.0.1":
  "integrity" "sha512-GK6QUtisv4fNS+XcI7shX0Gx9ORg7QqIznyfho79JTnX1XhLiyZHfftvGiziqzRiEi/Bjhgpi+D2o7HxJFPnDQ=="
  "resolved" "https://registry.npmjs.org/copy-anything/-/copy-anything-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "is-what" "^3.12.0"

"copy-to-clipboard@^3.3.1":
  "integrity" "sha512-i13qo6kIHTTpCm8/Wup+0b1mVWETvu2kIMzKoK8FpkLkFxlt0znUAHcMzox+T8sPlqtZXq3CulEjQHsYiGFJUw=="
  "resolved" "https://registry.npmjs.org/copy-to-clipboard/-/copy-to-clipboard-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "toggle-selection" "^1.0.6"

"core-js-compat@^3.18.0", "core-js-compat@^3.19.1":
  "integrity" "sha512-ObBY1W5vx/LFFMaL1P5Udo4Npib6fu+cMokeziWkA8Tns4FcDemKF5j9JvaI5JhdkW8EQJQGJN1EcrzmEwuAqQ=="
  "resolved" "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.19.2.tgz"
  "version" "3.19.2"
  dependencies:
    "browserslist" "^4.18.1"
    "semver" "7.0.0"

"cosmiconfig@^7.0.0", "cosmiconfig@^7.0.1":
  "integrity" "sha512-a1YWNUV2HwGimB7dU2s1wUMurNKjpx60HxBB6xUM8Re+2s1g1IIfJvFR0/iCF+XHdE0GMTKTuLR32UQff4TEyQ=="
  "resolved" "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "@types/parse-json" "^4.0.0"
    "import-fresh" "^3.2.1"
    "parse-json" "^5.0.0"
    "path-type" "^4.0.0"
    "yaml" "^1.10.0"

"cross-spawn@^7.0.0", "cross-spawn@^7.0.2":
  "integrity" "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"css-select-base-adapter@^0.1.1":
  "integrity" "sha512-jQVeeRG70QI08vSTwf1jHxp74JoZsr2XSgETae8/xC8ovSnL2WF87GTLO86Sbwdt2lK4Umg4HnnwMO4YF3Ce7w=="
  "resolved" "https://registry.npmjs.org/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz"
  "version" "0.1.1"

"css-select@^2.0.0":
  "integrity" "sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ=="
  "resolved" "https://registry.npmjs.org/css-select/-/css-select-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^3.2.1"
    "domutils" "^1.7.0"
    "nth-check" "^1.0.2"

"css-tree@^1.1.2":
  "integrity" "sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q=="
  "resolved" "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "mdn-data" "2.0.14"
    "source-map" "^0.6.1"

"css-tree@1.0.0-alpha.37":
  "integrity" "sha512-DMxWJg0rnz7UgxKT0Q1HU/L9BeJI0M6ksor0OgqOnF+aRCDWg/N2641HmVyU9KVIu0OVVWOb2IpC9A+BJRnejg=="
  "resolved" "https://registry.npmjs.org/css-tree/-/css-tree-1.0.0-alpha.37.tgz"
  "version" "1.0.0-alpha.37"
  dependencies:
    "mdn-data" "2.0.4"
    "source-map" "^0.6.1"

"css-what@^3.2.1":
  "integrity" "sha512-ACUm3L0/jiZTqfzRM3Hi9Q8eZqd6IK37mMWPLz9PJxkLWllYeRf+EHUSHYEtFop2Eqytaq1FizFVh7XfBnXCDQ=="
  "resolved" "https://registry.npmjs.org/css-what/-/css-what-3.4.2.tgz"
  "version" "3.4.2"

"cssesc@^3.0.0":
  "integrity" "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg=="
  "resolved" "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"csso@^4.0.2":
  "integrity" "sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA=="
  "resolved" "https://registry.npmjs.org/csso/-/csso-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "css-tree" "^1.1.2"

"csstype@^3.0.2", "csstype@^3.0.8":
  "integrity" "sha512-2u44ZG2OcNUO9HDp/Jl8C07x6pU/eTR3ncV91SiK3dhG9TWvRVsCoJw14Ckx5DgWkzGA3waZWO3d7pgqpUI/XA=="
  "resolved" "https://registry.npmjs.org/csstype/-/csstype-3.0.10.tgz"
  "version" "3.0.10"

"d3-array@^2.5.0":
  "integrity" "sha512-B0ErZK/66mHtEsR1TkPEEkwdy+WDesimkM5gpZr5Dsg54BiTA5RXtYW5qTLIAcekaS9xfZrzBLF/OAkB3Qn1YQ=="
  "resolved" "https://registry.npmjs.org/d3-array/-/d3-array-2.12.1.tgz"
  "version" "2.12.1"
  dependencies:
    "internmap" "^1.0.0"

"d3-array@1":
  "integrity" "sha512-KHW6M86R+FUPYGb3R5XiYjXPq7VzwxZ22buHhAEVG5ztoEcZZMLov530mmccaqA1GghZArjQV46fuc8kUqhhHw=="
  "resolved" "https://registry.npmjs.org/d3-array/-/d3-array-1.2.4.tgz"
  "version" "1.2.4"

"d3-collection@1":
  "integrity" "sha512-ii0/r5f4sjKNTfh84Di+DpztYwqKhEyUlKoPrzUFfeSkWxjW49xU2QzO9qrPrNkpdI0XJkfzvmTu8V2Zylln6A=="
  "resolved" "https://registry.npmjs.org/d3-collection/-/d3-collection-1.0.7.tgz"
  "version" "1.0.7"

"d3-color@^1.4.1", "d3-color@1":
  "integrity" "sha512-p2sTHSLCJI2QKunbGb7ocOh7DgTAn8IrLx21QRc/BSnodXM4sv6aLQlnfpvehFMLZEfBc6g9pH9SWQccFYfJ9Q=="
  "resolved" "https://registry.npmjs.org/d3-color/-/d3-color-1.4.1.tgz"
  "version" "1.4.1"

"d3-composite-projections@^1.2.0":
  "integrity" "sha512-csygyxdRfy7aUYRPea23veM6sjisdHI+DNd0nHcAGMd2LyL2lklr+xLRzHiJ+hy1HGp6YgAtbqdJR8CsLolrNQ=="
  "resolved" "https://registry.npmjs.org/d3-composite-projections/-/d3-composite-projections-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "d3-geo" "^2.0.1"
    "d3-path" "^2.0.0"

"d3-dsv@^1.0.5":
  "integrity" "sha512-9yVlqvZcSOMhCYzniHE7EVUws7Fa1zgw+/EAV2BxJoG3ME19V6BQFBwI855XQDsxyOuG7NibqRMTtiF/Qup46g=="
  "resolved" "https://registry.npmjs.org/d3-dsv/-/d3-dsv-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "commander" "2"
    "iconv-lite" "0.4"
    "rw" "1"

"d3-ease@^1.0.5":
  "integrity" "sha512-lx14ZPYkhNx0s/2HX5sLFUI3mbasHjSSpwO/KaaNACweVwxUruKyWVcb293wMv1RqTPZyZ8kSZ2NogUZNcLOFQ=="
  "resolved" "https://registry.npmjs.org/d3-ease/-/d3-ease-1.0.7.tgz"
  "version" "1.0.7"

"d3-geo-projection@~2.1.2":
  "integrity" "sha1-ffjh6dBG1jHGUJ9+UxNX1K3CSqM= sha512-zft6RRvPaB1qplTodBVcSH5Ftvmvvg0qoDiqpt+fyNthGr/qr+DD30cizNDluXjW7jmo7EKUTjvFCAHofv08Ow=="
  "resolved" "https://registry.npmjs.org/d3-geo-projection/-/d3-geo-projection-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "commander" "2"
    "d3-array" "1"
    "d3-geo" "^1.1.0"

"d3-geo@^1.1.0", "d3-geo@~1.6.4":
  "integrity" "sha1-8g4eRhyxhF9ai+Vatvh2VCp+MZk= sha512-O5Q3iftLc6/EdU1MHUm+O29NoKKN/cyQtySnD9/yEEcinN+q4ng+H56e2Yn1YWdfZBoiaRVtR2NoJ3ivKX5ptQ=="
  "resolved" "https://registry.npmjs.org/d3-geo/-/d3-geo-1.6.4.tgz"
  "version" "1.6.4"
  dependencies:
    "d3-array" "1"

"d3-geo@^2.0.1":
  "integrity" "sha512-8pM1WGMLGFuhq9S+FpPURxic+gKzjluCD/CHTuUF3mXMeiCo0i6R0tO1s4+GArRFde96SLcW/kOFRjoAosPsFA=="
  "resolved" "https://registry.npmjs.org/d3-geo/-/d3-geo-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "d3-array" "^2.5.0"

"d3-geo@1.7.1":
  "integrity" "sha512-O4AempWAr+P5qbk2bC2FuN/sDW4z+dN2wDf9QV3bxQt4M5HfOEeXLgJ/UKQW0+o1Dj8BE+L5kiDbdWUMjsmQpw=="
  "resolved" "https://registry.npmjs.org/d3-geo/-/d3-geo-1.7.1.tgz"
  "version" "1.7.1"
  dependencies:
    "d3-array" "1"

"d3-hexjson@^1.0.1":
  "integrity" "sha512-WMF1juFJwAx6LzdEVKlsCGZz+7QUG7VMJDtg8uD3cfNwWOTgMiy6qBRRGU7LSY2KbmEObu3BV5ZQbq9l/BvUZQ=="
  "resolved" "https://registry.npmjs.org/d3-hexjson/-/d3-hexjson-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "d3-array" "1"

"d3-hierarchy@^1.1.5":
  "integrity" "sha512-j8tPxlqh1srJHAtxfvOUwKNYJkQuBFdM1+JAUfq6xqH5eAqf93L7oG1NVqDa4CpFZNvnNKtCYEUC8KY9yEn9lQ=="
  "resolved" "https://registry.npmjs.org/d3-hierarchy/-/d3-hierarchy-1.1.9.tgz"
  "version" "1.1.9"

"d3-hierarchy@^2.0.0":
  "integrity" "sha512-SwIdqM3HxQX2214EG9GTjgmCc/mbSx4mQBn+DuEETubhOw6/U3fmnji4uCVrmzOydMHSO1nZle5gh6HB/wdOzw=="
  "resolved" "https://registry.npmjs.org/d3-hierarchy/-/d3-hierarchy-2.0.0.tgz"
  "version" "2.0.0"

"d3-interpolate@^1.3.2":
  "integrity" "sha512-V9znK0zc3jOPV4VD2zZn0sDhZU3WAE2bmlxdIwwQPPzPjvyLkd8B3JUVdS1IDUFDkWZ72c9qnv1GK2ZagTZ8EA=="
  "resolved" "https://registry.npmjs.org/d3-interpolate/-/d3-interpolate-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "d3-color" "1"

"d3-path@^2.0.0":
  "integrity" "sha512-ZwZQxKhBnv9yHaiWd6ZU4x5BtCQ7pXszEV9CU6kRgwIQVQGLMv1oiL4M+MK/n79sYzsj+gcgpPQSctJUsLN7fA=="
  "resolved" "https://registry.npmjs.org/d3-path/-/d3-path-2.0.0.tgz"
  "version" "2.0.0"

"d3-path@1":
  "integrity" "sha512-VLaYcn81dtHVTjEHd8B+pbe9yHWpXKZUC87PzoFmsFrJqgFwDe/qxfp5MlfsfM1V5E/iVt0MmEbWQ7FVIXh/bg=="
  "resolved" "https://registry.npmjs.org/d3-path/-/d3-path-1.0.9.tgz"
  "version" "1.0.9"

"d3-regression@^1.3.5":
  "integrity" "sha512-PoMpToIvxSnVpgAZTCERVseRend40JIBICJxwATJ/T4laWGaI5dpRdRxrPITxD8hk8W455fKonVChwSmDyWEyg=="
  "resolved" "https://registry.npmjs.org/d3-regression/-/d3-regression-1.3.9.tgz"
  "version" "1.3.9"

"d3-sankey@^0.9.1":
  "integrity" "sha512-nnRkDaUMjBdeuGg+kWGdA+tjG1AVTnJ+Ykw7ff7CZHVI17Hm5sy8n0UXykVffn13aNHwK5wPOdOt1gS1ZEaF+A=="
  "resolved" "https://registry.npmjs.org/d3-sankey/-/d3-sankey-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "d3-array" "1"
    "d3-collection" "1"
    "d3-shape" "^1.2.0"

"d3-shape@^1.2.0":
  "integrity" "sha512-EUkvKjqPFUAZyOlhY5gzCxCeI0Aep04LwIRpsZ/mLFelJiUfnK56jo5JMDSE7yyP2kLSb6LtF+S5chMk7uqPqw=="
  "resolved" "https://registry.npmjs.org/d3-shape/-/d3-shape-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "d3-path" "1"

"d3-timer@^1.0.9":
  "integrity" "sha512-B1JDm0XDaQC+uvo4DT79H0XmBskgS3l6Ve+1SBCfxgmtIb1AVrPIoqd+nPSv+loMX8szQ0sVUhGngL7D5QPiXw=="
  "resolved" "https://registry.npmjs.org/d3-timer/-/d3-timer-1.0.10.tgz"
  "version" "1.0.10"

"d3-voronoi@^1.1.2":
  "integrity" "sha512-dArJ32hchFsrQ8uMiTBLq256MpnZjeuBtdHpaDlYuQyjU0CVzCJl/BVW+SkszaAeH95D/8gxqAhgx0ouAWAfRg=="
  "resolved" "https://registry.npmjs.org/d3-voronoi/-/d3-voronoi-1.1.4.tgz"
  "version" "1.1.4"

"d3-voronoi@1.1.2":
  "integrity" "sha1-Fodmfo8TotFYyAwUgMWinLDYlzw= sha512-RhGS1u2vavcO7ay7ZNAPo4xeDh/VYeGof3x5ZLJBQgYhLegxr3s5IykvWmJ94FTU6mcbtp4sloqZ54mP6R4Utw=="
  "resolved" "https://registry.npmjs.org/d3-voronoi/-/d3-voronoi-1.1.2.tgz"
  "version" "1.1.2"

"dagre@^0.8.2":
  "integrity" "sha512-/aTqmnRta7x7MCCpExk7HQL2O4owCT2h8NT//9I1OQ9vt29Pa0BzSAkR5lwFUcQ7491yVi/3CXU9jQ5o0Mn2Sw=="
  "resolved" "https://registry.npmjs.org/dagre/-/dagre-0.8.5.tgz"
  "version" "0.8.5"
  dependencies:
    "graphlib" "^2.1.8"
    "lodash" "^4.17.15"

"dayjs@^1.10.5":
  "integrity" "sha512-P6twpd70BcPK34K26uJ1KT3wlhpuOAPoMwJzpsIWUxHZ7wpmbdZL/hQqBDfz7hGurYSa5PhzdhDHtt319hL3ig=="
  "resolved" "https://registry.npmjs.org/dayjs/-/dayjs-1.10.7.tgz"
  "version" "1.10.7"

"debug@^3.2.6":
  "integrity" "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.1.0", "debug@^4.1.1", "debug@^4.3.1", "debug@^4.3.2":
  "integrity" "sha512-/zxw5+vh1Tfv+4Qn7a5nsbcJKPaSvCDhojn6FEl9vupwK2VCSDtEiEtqr8DFtzYFOdz63LBkxec7DYuc2jon6Q=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.3.tgz"
  "version" "4.3.3"
  dependencies:
    "ms" "2.1.2"

"decamelize-keys@^1.1.0":
  "integrity" "sha1-0XGoeTMlKAfrPLYdwcFEXQeN8tk= sha512-ocLWuYzRPoS9bfiSdDd3cxvrzovVMZnRDVEzAs+hWIVXGDbHxWMECij2OBuyB/An0FFW/nLuq6Kv1i/YC5Qfzg=="
  "resolved" "https://registry.npmjs.org/decamelize-keys/-/decamelize-keys-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "decamelize" "^1.1.0"
    "map-obj" "^1.0.0"

"decamelize@^1.0.0", "decamelize@^1.1.0", "decamelize@^1.2.0":
  "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA= sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA=="
  "resolved" "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decode-uri-component@^0.2.0":
  "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU= sha512-hjf+xovcEn31w/EUYdTXQh/8smFL/dzYjohQGEIgjyNavaJfBY2p5F527Bo1VPATxv0VYTUC2bOcXvqFwk78Og=="
  "resolved" "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"deep-equal@^1.0.0", "deep-equal@~1.1.1", "deep-equal@1.x":
  "integrity" "sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g=="
  "resolved" "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "is-arguments" "^1.0.4"
    "is-date-object" "^1.0.1"
    "is-regex" "^1.0.4"
    "object-is" "^1.0.1"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.2.0"

"deep-is@^0.1.3":
  "integrity" "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ=="
  "resolved" "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"deepmerge@^4.2.2":
  "integrity" "sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg=="
  "resolved" "https://registry.npmjs.org/deepmerge/-/deepmerge-4.2.2.tgz"
  "version" "4.2.2"

"define-properties@^1.1.3":
  "integrity" "sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ=="
  "resolved" "https://registry.npmjs.org/define-properties/-/define-properties-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "object-keys" "^1.0.12"

"defined@~1.0.0":
  "integrity" "sha1-yY2bzvdWdBiOEQlpFRGZ45sfppM= sha512-Y2caI5+ZwS5c3RiNDJ6u53VhQHv+hHKwhkI1iHvceKUHw9Df6EK2zRLfjejRgMuCuxK7PfSWIMwWecceVvThjQ=="
  "resolved" "https://registry.npmjs.org/defined/-/defined-1.0.0.tgz"
  "version" "1.0.0"

"density-clustering@1.3.0":
  "integrity" "sha1-3J9ZyPCrl+FiSsZJMP0xlIF9ysU= sha512-icpmBubVTwLnsaor9qH/4tG5+7+f61VcqMN3V3pm9sxxSCt2Jcs0zWOgwZW9ARJYaKD3FumIgHiMOcIMRRAzFQ=="
  "resolved" "https://registry.npmjs.org/density-clustering/-/density-clustering-1.3.0.tgz"
  "version" "1.3.0"

"detect-browser@^5.0.0", "detect-browser@^5.1.0":
  "integrity" "sha512-eAcRiEPTs7utXWPaAgu/OX1HRJpxW7xSHpw4LTDrGFaeWnJ37HRlqpUkKsDm0AoTbtrvHQhH+5U2Cd87EGhJTg=="
  "resolved" "https://registry.npmjs.org/detect-browser/-/detect-browser-5.2.1.tgz"
  "version" "5.2.1"

"detect-node-es@^1.1.0":
  "integrity" "sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ=="
  "resolved" "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz"
  "version" "1.1.0"

"dir-glob@^3.0.1":
  "integrity" "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA=="
  "resolved" "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "path-type" "^4.0.0"

"doctrine@^2.1.0":
  "integrity" "sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw=="
  "resolved" "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esutils" "^2.0.2"

"doctrine@^3.0.0":
  "integrity" "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w=="
  "resolved" "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"dom-helpers@^5.0.1":
  "integrity" "sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA=="
  "resolved" "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "@babel/runtime" "^7.8.7"
    "csstype" "^3.0.2"

"dom-serializer@0":
  "integrity" "sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g=="
  "resolved" "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "entities" "^2.0.0"

"domelementtype@^2.0.1":
  "integrity" "sha512-DtBMo82pv1dFtUmHyr48beiuq792Sxohr+8Hm9zoxklYPfa6n0Z3Byjj2IV7bmr2IyqClnqEQhfgHJJ5QF0R5A=="
  "resolved" "https://registry.npmjs.org/domelementtype/-/domelementtype-2.2.0.tgz"
  "version" "2.2.0"

"domelementtype@1":
  "integrity" "sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w=="
  "resolved" "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domutils@^1.7.0":
  "integrity" "sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg=="
  "resolved" "https://registry.npmjs.org/domutils/-/domutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"dotignore@~0.1.2":
  "integrity" "sha512-UGGGWfSauusaVJC+8fgV+NVvBXkCTmVv7sk6nojDZZvuOUNGUy0Zk4UpHQD6EDjS0jpBwcACvH4eofvyzBcRDw=="
  "resolved" "https://registry.npmjs.org/dotignore/-/dotignore-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "minimatch" "^3.0.4"

"earcut@^2.0.0":
  "integrity" "sha512-iRDI1QeCQIhMCZk48DRDMVgQSSBDmbzzNhnxIo+pwx3swkfjMh6vh0nWLq1NdvGHLKH6wIrAM3vQWeTj6qeoug=="
  "resolved" "https://registry.npmjs.org/earcut/-/earcut-2.2.3.tgz"
  "version" "2.2.3"

"electron-to-chromium@^1.5.73":
  "integrity" "sha512-u1z9VuzDXV86X2r3vAns0/5ojfXBue9o0+JDUDBKYqGLjxLkSqsSUoPU/6kW0gx76V44frHaf6Zo+QF74TQCMg=="
  "resolved" "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.100.tgz"
  "version" "1.5.100"

"emoji-regex@^8.0.0":
  "integrity" "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="
  "resolved" "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emojis-list@^3.0.0":
  "integrity" "sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q=="
  "resolved" "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"end-of-stream@^1.1.0":
  "integrity" "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q=="
  "resolved" "https://registry.npmmirror.com/end-of-stream/-/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"enhanced-resolve@^5.17.1":
  "integrity" "sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg=="
  "resolved" "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.1.tgz"
  "version" "5.18.1"
  dependencies:
    "graceful-fs" "^4.2.4"
    "tapable" "^2.2.0"

"entities@^2.0.0":
  "integrity" "sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A=="
  "resolved" "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz"
  "version" "2.2.0"

"entities@^3.0.1":
  "integrity" "sha512-WiyBqoomrwMdFG1e0kqvASYfnlb0lp8M5o5Fw2OFq1hNZxxcNk8Ik0Xm7LxzBhuidnZB/UtBqVCgUz3kBOP51Q=="
  "resolved" "https://registry.npmjs.org/entities/-/entities-3.0.1.tgz"
  "version" "3.0.1"

"errno@^0.1.1":
  "integrity" "sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A=="
  "resolved" "https://registry.npmjs.org/errno/-/errno-0.1.8.tgz"
  "version" "0.1.8"
  dependencies:
    "prr" "~1.0.1"

"error-ex@^1.3.1":
  "integrity" "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="
  "resolved" "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"es-abstract@^1.17.2", "es-abstract@^1.19.0", "es-abstract@^1.19.1":
  "integrity" "sha512-2vJ6tjA/UfqLm2MPs7jxVybLoB8i1t1Jd9R3kISld20sIxPcTbLuggQOUxeWeAvIUkduv/CfMjuh4WmiXr2v9w=="
  "resolved" "https://registry.npmjs.org/es-abstract/-/es-abstract-1.19.1.tgz"
  "version" "1.19.1"
  dependencies:
    "call-bind" "^1.0.2"
    "es-to-primitive" "^1.2.1"
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.1.1"
    "get-symbol-description" "^1.0.0"
    "has" "^1.0.3"
    "has-symbols" "^1.0.2"
    "internal-slot" "^1.0.3"
    "is-callable" "^1.2.4"
    "is-negative-zero" "^2.0.1"
    "is-regex" "^1.1.4"
    "is-shared-array-buffer" "^1.0.1"
    "is-string" "^1.0.7"
    "is-weakref" "^1.0.1"
    "object-inspect" "^1.11.0"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.2"
    "string.prototype.trimend" "^1.0.4"
    "string.prototype.trimstart" "^1.0.4"
    "unbox-primitive" "^1.0.1"

"es-module-lexer@^1.2.1":
  "integrity" "sha512-qqnD1yMU6tk/jnaMosogGySTZP8YtUgAffA9nMN+E/rjxcfRQ6IEk7IiozUjgxKoFHBGjTLnrHB/YC45r/59EQ=="
  "resolved" "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.6.0.tgz"
  "version" "1.6.0"

"es-to-primitive@^1.2.1":
  "integrity" "sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA=="
  "resolved" "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "is-callable" "^1.1.4"
    "is-date-object" "^1.0.1"
    "is-symbol" "^1.0.2"

"esbuild-darwin-64@0.14.54":
  "integrity" "sha512-jtdKWV3nBviOd5v4hOpkVmpxsBy90CGzebpbO9beiqUYVMBtSc0AL9zGftFuBon7PNDcdvNCEuQqw2x0wP9yug=="
  "resolved" "https://registry.npmmirror.com/esbuild-darwin-64/-/esbuild-darwin-64-0.14.54.tgz"
  "version" "0.14.54"

"esbuild@^0.14.27":
  "integrity" "sha512-Cy9llcy8DvET5uznocPyqL3BFRrFXSVqbgpMJ9Wz8oVjZlh/zUSNbPRbov0VX7VxN2JH1Oa0uNxZ7eLRb62pJA=="
  "resolved" "https://registry.npmmirror.com/esbuild/-/esbuild-0.14.54.tgz"
  "version" "0.14.54"
  optionalDependencies:
    "@esbuild/linux-loong64" "0.14.54"
    "esbuild-android-64" "0.14.54"
    "esbuild-android-arm64" "0.14.54"
    "esbuild-darwin-64" "0.14.54"
    "esbuild-darwin-arm64" "0.14.54"
    "esbuild-freebsd-64" "0.14.54"
    "esbuild-freebsd-arm64" "0.14.54"
    "esbuild-linux-32" "0.14.54"
    "esbuild-linux-64" "0.14.54"
    "esbuild-linux-arm" "0.14.54"
    "esbuild-linux-arm64" "0.14.54"
    "esbuild-linux-mips64le" "0.14.54"
    "esbuild-linux-ppc64le" "0.14.54"
    "esbuild-linux-riscv64" "0.14.54"
    "esbuild-linux-s390x" "0.14.54"
    "esbuild-netbsd-64" "0.14.54"
    "esbuild-openbsd-64" "0.14.54"
    "esbuild-sunos-64" "0.14.54"
    "esbuild-windows-32" "0.14.54"
    "esbuild-windows-64" "0.14.54"
    "esbuild-windows-arm64" "0.14.54"

"escalade@^3.2.0":
  "integrity" "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA=="
  "resolved" "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  "version" "3.2.0"

"escape-string-regexp@^1.0.2":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ= sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escape-string-regexp@^1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ= sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escape-string-regexp@^4.0.0":
  "integrity" "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"eslint-config-prettier@^8.3.0":
  "integrity" "sha512-BgZuLUSeKzvlL/VUjx/Yb787VQ26RU3gGjA3iiFvdsp/2bMfVIWUVP7tjxtjS0e+HP409cPlPvNkQloz8C91ew=="
  "resolved" "https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-8.3.0.tgz"
  "version" "8.3.0"

"eslint-plugin-babel@^5.3.1":
  "integrity" "sha512-VsQEr6NH3dj664+EyxJwO4FCYm/00JhYb3Sk3ft8o+fpKuIfQ9TaW6uVUfvwMXHcf/lsnRIoyFPsLMyiWCSL/g=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-babel/-/eslint-plugin-babel-5.3.1.tgz"
  "version" "5.3.1"
  dependencies:
    "eslint-rule-composer" "^0.3.0"

"eslint-plugin-prettier@^4.0.0":
  "integrity" "sha512-98MqmCJ7vJodoQK359bqQWaxOE0CS8paAz/GgjaZLyex4TTk3g9HugoO89EqWCrFiOqn9EVvcoo7gZzONCWVwQ=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-prettier/-/eslint-plugin-prettier-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "prettier-linter-helpers" "^1.0.0"

"eslint-plugin-react-hooks@^4.3.0":
  "integrity" "sha512-XslZy0LnMn+84NEG9jSGR6eGqaZB3133L8xewQo3fQagbQuGt7a63gf+P1NGKZavEYEC3UXaWEAA/AqDkuN6xA=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.3.0.tgz"
  "version" "4.3.0"

"eslint-plugin-react@^7.27.1":
  "integrity" "sha512-meyunDjMMYeWr/4EBLTV1op3iSG3mjT/pz5gti38UzfM4OPpNc2m0t2xvKCOMU5D6FSdd34BIMFOvQbW+i8GAA=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "array-includes" "^3.1.4"
    "array.prototype.flatmap" "^1.2.5"
    "doctrine" "^2.1.0"
    "estraverse" "^5.3.0"
    "jsx-ast-utils" "^2.4.1 || ^3.0.0"
    "minimatch" "^3.0.4"
    "object.entries" "^1.1.5"
    "object.fromentries" "^2.0.5"
    "object.hasown" "^1.1.0"
    "object.values" "^1.1.5"
    "prop-types" "^15.7.2"
    "resolve" "^2.0.0-next.3"
    "semver" "^6.3.0"
    "string.prototype.matchall" "^4.0.6"

"eslint-rule-composer@^0.3.0":
  "integrity" "sha512-bt+Sh8CtDmn2OajxvNO+BX7Wn4CIWMpTRm3MaiKPCQcnnlm0CS2mhui6QaoeQugs+3Kj2ESKEEGJUdVafwhiCg=="
  "resolved" "https://registry.npmjs.org/eslint-rule-composer/-/eslint-rule-composer-0.3.0.tgz"
  "version" "0.3.0"

"eslint-scope@^5.1.1", "eslint-scope@5.1.1":
  "integrity" "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-scope@^7.1.1":
  "integrity" "sha512-QKQM/UXpIiHcLqJ5AOyIW7XZmzjkzQXYE54n1++wb0u9V/abW3l9uQnxX8Z5Xd18xyKIMTUAyQ0k1e8pz6LUrw=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^5.2.0"

"eslint-utils@^3.0.0":
  "integrity" "sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA=="
  "resolved" "https://registry.npmjs.org/eslint-utils/-/eslint-utils-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "eslint-visitor-keys" "^2.0.0"

"eslint-visitor-keys@^2.0.0":
  "integrity" "sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
  "version" "2.1.0"

"eslint-visitor-keys@^3.0.0", "eslint-visitor-keys@^3.3.0":
  "integrity" "sha512-mQ+suqKJVyeuwGYHAdjMFqjCyfl8+Ldnxuyp3ldiMBFKkvytrXUZWaiPCEav8qDHKty44bD+qV1IP4T+w+xXRA=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.3.0.tgz"
  "version" "3.3.0"

"eslint@*", "eslint@^3 || ^4 || ^5 || ^6 || ^7 || ^8", "eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0", "eslint@^6.0.0 || ^7.0.0 || ^8.0.0", "eslint@^8.10.0", "eslint@>=4.0.0", "eslint@>=5", "eslint@>=7.0.0", "eslint@>=7.28.0":
  "integrity" "sha512-tcI1D9lfVec+R4LE1mNDnzoJ/f71Kl/9Cv4nG47jOueCMBrCCKYXr4AUVS7go6mWYGFD4+EoN6+eXSrEbRzXVw=="
  "resolved" "https://registry.npmjs.org/eslint/-/eslint-8.10.0.tgz"
  "version" "8.10.0"
  dependencies:
    "@eslint/eslintrc" "^1.2.0"
    "@humanwhocodes/config-array" "^0.9.2"
    "ajv" "^6.10.0"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.2"
    "debug" "^4.3.2"
    "doctrine" "^3.0.0"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^7.1.1"
    "eslint-utils" "^3.0.0"
    "eslint-visitor-keys" "^3.3.0"
    "espree" "^9.3.1"
    "esquery" "^1.4.0"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^6.0.1"
    "functional-red-black-tree" "^1.0.1"
    "glob-parent" "^6.0.1"
    "globals" "^13.6.0"
    "ignore" "^5.2.0"
    "import-fresh" "^3.0.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "js-yaml" "^4.1.0"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.4.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.0.4"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.1"
    "regexpp" "^3.2.0"
    "strip-ansi" "^6.0.1"
    "strip-json-comments" "^3.1.0"
    "text-table" "^0.2.0"
    "v8-compile-cache" "^2.0.3"

"espree@^9.3.1":
  "integrity" "sha512-bvdyLmJMfwkV3NCRl5ZhJf22zBFo1y8bYh3VYb+bfzqNB4Je68P2sSuXyuFquzWLebHpNd2/d5uv7yoP9ISnGQ=="
  "resolved" "https://registry.npmjs.org/espree/-/espree-9.3.1.tgz"
  "version" "9.3.1"
  dependencies:
    "acorn" "^8.7.0"
    "acorn-jsx" "^5.3.1"
    "eslint-visitor-keys" "^3.3.0"

"esprima@^4.0.0":
  "integrity" "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="
  "resolved" "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.4.0":
  "integrity" "sha512-cCDispWt5vHHtwMY2YrAQ4ibFkAL8RbH5YGBnZBc90MolvvfkkQcJro/aZiAQUlQ3qgrYS6D6v8Gc5G5CQsc9w=="
  "resolved" "https://registry.npmjs.org/esquery/-/esquery-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.3.0":
  "integrity" "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag=="
  "resolved" "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0", "estraverse@^5.2.0", "estraverse@^5.3.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"estree-walker@^2.0.1":
  "integrity" "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w=="
  "resolved" "https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz"
  "version" "2.0.2"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"events@^3.2.0":
  "integrity" "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q=="
  "resolved" "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  "version" "3.3.0"

"execa@^4.1.0":
  "integrity" "sha512-j5W0//W7f8UxAn8hXVnwG8tLwdiUy4FJLcSupCg6maBYZDpyBvTApK7KyuI4bKj8KOh1r2YH+6ucuYtJv1bTZA=="
  "resolved" "https://registry.npmmirror.com/execa/-/execa-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "cross-spawn" "^7.0.0"
    "get-stream" "^5.0.0"
    "human-signals" "^1.1.1"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.0"
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"
    "strip-final-newline" "^2.0.0"

"execall@^2.0.0":
  "integrity" "sha512-0FU2hZ5Hh6iQnarpRtQurM/aAvp3RIbfvgLHrcqJYzhXyV2KFruhuChf9NC6waAhiUR7FFtlugkI4p7f2Fqlow=="
  "resolved" "https://registry.npmjs.org/execall/-/execall-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "clone-regexp" "^2.1.0"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-diff@^1.1.2":
  "integrity" "sha512-xJuoT5+L99XlZ8twedaRf6Ax2TgQVxvgZOYoPKqZufmJib0tL2tegPBOZb1pVNgIhlqDlA0eO0c3wBvQcmzx4w=="
  "resolved" "https://registry.npmjs.org/fast-diff/-/fast-diff-1.2.0.tgz"
  "version" "1.2.0"

"fast-glob@^3.1.1", "fast-glob@^3.2.7":
  "integrity" "sha512-rYGMRwip6lUMvYD3BTScMwT1HtAs2d71SMv66Vrxs0IekGZEjhM0pcMfjQPnknBt2zeCwQMEupiN02ZP4DiT1Q=="
  "resolved" "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    "glob-parent" "^5.1.2"
    "merge2" "^1.3.0"
    "micromatch" "^4.0.4"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc= sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw=="
  "resolved" "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fast-uri@^3.0.1":
  "integrity" "sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw=="
  "resolved" "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.6.tgz"
  "version" "3.0.6"

"fastest-levenshtein@^1.0.12":
  "integrity" "sha512-On2N+BpYJ15xIC974QNVuYGMOlEVt4s0EOI3wwMqOmK1fdDY+FN/zltPV8vosq4ad4c/gJ1KHScUn/6AWIgiow=="
  "resolved" "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.12.tgz"
  "version" "1.0.12"

"fastq@^1.6.0":
  "integrity" "sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw=="
  "resolved" "https://registry.npmjs.org/fastq/-/fastq-1.13.0.tgz"
  "version" "1.13.0"
  dependencies:
    "reusify" "^1.0.4"

"fecha@~4.2.0":
  "integrity" "sha512-MMMQ0ludy/nBs1/o0zVOiKTpG7qMbonKUzjJgQFEuvq6INZ1OraKPRAWkBq5vlKLOUMpmNYG1JoN3oDPUQ9m3Q=="
  "resolved" "https://registry.npmjs.org/fecha/-/fecha-4.2.1.tgz"
  "version" "4.2.1"

"file-entry-cache@^6.0.1":
  "integrity" "sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg=="
  "resolved" "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "flat-cache" "^3.0.4"

"fill-range@^7.0.1":
  "integrity" "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"filter-obj@^1.1.0":
  "integrity" "sha1-mzERErxsYSehbgFsbF1/GeCAXFs= sha512-8rXg1ZnX7xzy2NGDVkBVaAy+lSlPNwad13BtgSlLuxfIslyt5Vg64U7tFcCt4WS1R0hvtnQybT/IyCkGZ3DpXQ=="
  "resolved" "https://registry.npmjs.org/filter-obj/-/filter-obj-1.1.0.tgz"
  "version" "1.1.0"

"find-up@^4.1.0":
  "integrity" "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"flat-cache@^3.0.4":
  "integrity" "sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg=="
  "resolved" "https://registry.npmjs.org/flat-cache/-/flat-cache-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "flatted" "^3.1.0"
    "rimraf" "^3.0.2"

"flatted@^3.1.0":
  "integrity" "sha512-8/sOawo8tJ4QOBX8YlQBMxL8+RLZfxMQOif9o0KUKTNTjMYElWPE0r/m5VNFxTRd0NSw8qSy8dajrwX4RYI1Hw=="
  "resolved" "https://registry.npmjs.org/flatted/-/flatted-3.2.4.tgz"
  "version" "3.2.4"

"fmin@^0.0.2":
  "integrity" "sha1-Wbu0DUP/3ByUzQClaMQflfGXMBc= sha512-sSi6DzInhl9d8yqssDfGZejChO8d2bAGIpysPsvYsxFe898z89XhCZg6CPNV3nhUhFefeC/AXZK2bAJxlBjN6A=="
  "resolved" "https://registry.npmjs.org/fmin/-/fmin-0.0.2.tgz"
  "version" "0.0.2"
  dependencies:
    "contour_plot" "^0.0.1"
    "json2module" "^0.0.3"
    "rollup" "^0.25.8"
    "tape" "^4.5.1"
    "uglify-js" "^2.6.2"

"focus-lock@^1.3.5":
  "integrity" "sha512-Ik/6OCk9RQQ0T5Xw+hKNLWrjSMtv51dD4GRmJjbD5a58TIEpI5a5iXagKVl3Z5UuyslMCA8Xwnu76jQob62Yhg=="
  "resolved" "https://registry.npmmirror.com/focus-lock/-/focus-lock-1.3.6.tgz"
  "version" "1.3.6"
  dependencies:
    "tslib" "^2.0.3"

"follow-redirects@^1.14.4":
  "integrity" "sha512-wtphSXy7d4/OR+MvIFbCVBDzZ5520qV8XfPklSN5QtxuMUJZ+b0Wnst1e1lCDocfzuCkHqj8k0FpZqO+UIaKNA=="
  "resolved" "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.14.5.tgz"
  "version" "1.14.5"

"for-each@~0.3.3":
  "integrity" "sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw=="
  "resolved" "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "is-callable" "^1.1.3"

"fs-extra@^10.0.0":
  "integrity" "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ=="
  "resolved" "https://registry.npmmirror.com/fs-extra/-/fs-extra-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "graceful-fs" "^4.2.0"
    "jsonfile" "^6.0.1"
    "universalify" "^2.0.0"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8= sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw=="
  "resolved" "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"fsevents@~2.3.2":
  "integrity" "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA=="
  "resolved" "https://registry.npmjs.org/fsevents/-/fsevents-2.3.2.tgz"
  "version" "2.3.2"

"function-bind@^1.1.1", "function-bind@^1.1.2":
  "integrity" "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="
  "resolved" "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz"
  "version" "1.1.2"

"functional-red-black-tree@^1.0.1":
  "integrity" "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc= sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g=="
  "resolved" "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"gensync@^1.0.0-beta.2":
  "integrity" "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg=="
  "resolved" "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"geojson-equality@0.1.6":
  "integrity" "sha1-oXE3TvBD5dR5eZWEC65GSOB1LXI= sha512-TqG8YbqizP3EfwP5Uw4aLu6pKkg6JQK9uq/XZ1lXQntvTHD1BBKJWhNpJ2M0ax6TuWMP3oyx6Oq7FCIfznrgpQ=="
  "resolved" "https://registry.npmjs.org/geojson-equality/-/geojson-equality-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "deep-equal" "^1.0.0"

"geojson-rbush@3.x":
  "integrity" "sha512-oVltQTXolxvsz1sZnutlSuLDEcQAKYC/uXt9zDzJJ6bu0W+baTI8LZBaTup5afzibEH4N3jlq2p+a152wlBJ7w=="
  "resolved" "https://registry.npmjs.org/geojson-rbush/-/geojson-rbush-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "@turf/bbox" "*"
    "@turf/helpers" "6.x"
    "@turf/meta" "6.x"
    "@types/geojson" "7946.0.8"
    "rbush" "^3.0.1"

"get-intrinsic@^1.0.2", "get-intrinsic@^1.1.0", "get-intrinsic@^1.1.1":
  "integrity" "sha512-kWZrnVM42QCiEA2Ig1bG8zjoIMOgxWwYCEeNdwY6Tv/cOSeGpcoX4pXHfKUxNKVoArnrEr2e9srnAxxGIraS9Q=="
  "resolved" "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "function-bind" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.1"

"get-stdin@^8.0.0":
  "integrity" "sha512-sY22aA6xchAzprjyqmSEQv4UbAAzRN0L2dQB0NlN5acTTK9Don6nhoc3eAbUnpZiCANAMfd/+40kVdKfFygohg=="
  "resolved" "https://registry.npmjs.org/get-stdin/-/get-stdin-8.0.0.tgz"
  "version" "8.0.0"

"get-stream@^5.0.0":
  "integrity" "sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA=="
  "resolved" "https://registry.npmmirror.com/get-stream/-/get-stream-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "pump" "^3.0.0"

"get-symbol-description@^1.0.0":
  "integrity" "sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw=="
  "resolved" "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "call-bind" "^1.0.2"
    "get-intrinsic" "^1.1.1"

"gl-matrix@^3.0.0", "gl-matrix@^3.3.0", "gl-matrix@^3.4.3":
  "integrity" "sha512-wcCp8vu8FT22BnvKVPjXa/ICBWRq/zjFfdofZy1WSpQZpphblv12/bOQLBC1rMM7SGOFS9ltVmKOHil5+Ml7gA=="
  "resolved" "https://registry.npmjs.org/gl-matrix/-/gl-matrix-3.4.3.tgz"
  "version" "3.4.3"

"glob-parent@^5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@^6.0.1":
  "integrity" "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "is-glob" "^4.0.3"

"glob-to-regexp@^0.4.1":
  "integrity" "sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw=="
  "resolved" "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  "version" "0.4.1"

"glob@^7.1.3":
  "integrity" "sha512-lmLf6gtyrPq8tTjSmrO94wBeQbFR3HbLHbuyD69wuyQkImp2hWqMGB47OX65FBkPffO641IP9jWa1z4ivqG26Q=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@~7.1.7":
  "integrity" "sha512-OvD9ENzPLbegENnYP5UUfJIirTg4+XwMWGaQfQTY0JenxNvvIKP3U3/tAQSPIu/lHxXYSZmpXlUHeqAIdKzBLQ=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-7.1.7.tgz"
  "version" "7.1.7"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"global-modules@^2.0.0":
  "integrity" "sha512-NGbfmJBp9x8IxyJSd1P+otYK8vonoJactOogrVfFRIAEY1ukil8RSKDz2Yo7wh1oihl51l/r6W4epkeKJHqL8A=="
  "resolved" "https://registry.npmjs.org/global-modules/-/global-modules-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "global-prefix" "^3.0.0"

"global-prefix@^3.0.0":
  "integrity" "sha512-awConJSVCHVGND6x3tmMaKcQvwXLhjdkmomy2W+Goaui8YPgYgXJZewhg3fWC+DlfqqQuWg8AwqjGTD2nAPVWg=="
  "resolved" "https://registry.npmjs.org/global-prefix/-/global-prefix-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ini" "^1.3.5"
    "kind-of" "^6.0.2"
    "which" "^1.3.1"

"globals@^11.1.0":
  "integrity" "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^13.6.0":
  "integrity" "sha512-317dFlgY2pdJZ9rspXDks7073GpDmXdfbM3vYYp0HAMKGDh1FfWPleI2ljVNLQX5M5lXcAslTcPTrOrMEFOjyw=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-13.12.1.tgz"
  "version" "13.12.1"
  dependencies:
    "type-fest" "^0.20.2"

"globals@^13.9.0":
  "integrity" "sha512-317dFlgY2pdJZ9rspXDks7073GpDmXdfbM3vYYp0HAMKGDh1FfWPleI2ljVNLQX5M5lXcAslTcPTrOrMEFOjyw=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-13.12.1.tgz"
  "version" "13.12.1"
  dependencies:
    "type-fest" "^0.20.2"

"globby@^11.0.4":
  "integrity" "sha512-9O4MVG9ioZJ08ffbcyVYyLOJLk5JQ688pJ4eMGLpdWLHq/Wr1D9BlriLQyL0E+jbkuePVZXYFj47QM/v093wHg=="
  "resolved" "https://registry.npmjs.org/globby/-/globby-11.0.4.tgz"
  "version" "11.0.4"
  dependencies:
    "array-union" "^2.1.0"
    "dir-glob" "^3.0.1"
    "fast-glob" "^3.1.1"
    "ignore" "^5.1.4"
    "merge2" "^1.3.0"
    "slash" "^3.0.0"

"globjoin@^0.1.4":
  "integrity" "sha1-L0SUrIkZ43Z8XLtpHp9GMyQoXUM= sha512-xYfnw62CKG8nLkZBfWbhWwDw02CHty86jfPcc2cr3ZfeuK9ysoVPPEUxf21bAD/rWAgk52SuBrLJlefNy8mvFg=="
  "resolved" "https://registry.npmjs.org/globjoin/-/globjoin-0.1.4.tgz"
  "version" "0.1.4"

"graceful-fs@^4.1.2", "graceful-fs@^4.1.6", "graceful-fs@^4.2.0", "graceful-fs@^4.2.11", "graceful-fs@^4.2.4":
  "integrity" "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ=="
  "resolved" "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz"
  "version" "4.2.11"

"graphlib@^2.1.8":
  "integrity" "sha512-jcLLfkpoVGmH7/InMC/1hIvOPSUh38oJtGhvrOFGzioE1DZ+0YW16RgmOJhHiuWTvGiJQ9Z1Ik43JvkRPRvE+A=="
  "resolved" "https://registry.npmjs.org/graphlib/-/graphlib-2.1.8.tgz"
  "version" "2.1.8"
  dependencies:
    "lodash" "^4.17.15"

"hard-rejection@^2.1.0":
  "integrity" "sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA=="
  "resolved" "https://registry.npmjs.org/hard-rejection/-/hard-rejection-2.1.0.tgz"
  "version" "2.1.0"

"has-ansi@^2.0.0":
  "integrity" "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE= sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg=="
  "resolved" "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-regex" "^2.0.0"

"has-bigints@^1.0.1":
  "integrity" "sha512-LSBS2LjbNBTf6287JEbEzvJgftkF5qFkmCo9hDRpAzKhUOlJ+hx8dd4USs00SgsUNwc4617J9ki5YtEClM2ffA=="
  "resolved" "https://registry.npmjs.org/has-bigints/-/has-bigints-1.0.1.tgz"
  "version" "1.0.1"

"has-flag@^3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0= sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-symbols@^1.0.1", "has-symbols@^1.0.2":
  "integrity" "sha512-chXa79rL/UC2KlX17jo3vRGz0azaWEx5tGqZg5pO3NUyEJVB17dMruQlzCCOfUvElghKcm5194+BCRvi2Rv/Gw=="
  "resolved" "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.2.tgz"
  "version" "1.0.2"

"has-tostringtag@^1.0.0":
  "integrity" "sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ=="
  "resolved" "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-symbols" "^1.0.2"

"has@^1.0.3", "has@~1.0.3":
  "integrity" "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw=="
  "resolved" "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hasown@^2.0.2":
  "integrity" "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ=="
  "resolved" "https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "function-bind" "^1.1.2"

"history@^4.9.0":
  "integrity" "sha512-36nwAD620w12kuzPAsyINPWJqlNbij+hpK1k9XRloDtym8mxzGYl2c17LnV6IAGB2Dmg4tEa7G7DlawS0+qjew=="
  "resolved" "https://registry.npmjs.org/history/-/history-4.10.1.tgz"
  "version" "4.10.1"
  dependencies:
    "@babel/runtime" "^7.1.2"
    "loose-envify" "^1.2.0"
    "resolve-pathname" "^3.0.0"
    "tiny-invariant" "^1.0.2"
    "tiny-warning" "^1.0.0"
    "value-equal" "^1.0.1"

"hoist-non-react-statics@^3.1.0", "hoist-non-react-statics@^3.3.0", "hoist-non-react-statics@^3.3.1", "hoist-non-react-statics@^3.3.2":
  "integrity" "sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw=="
  "resolved" "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "react-is" "^16.7.0"

"hosted-git-info@^2.1.4":
  "integrity" "sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw=="
  "resolved" "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.9.tgz"
  "version" "2.8.9"

"hosted-git-info@^4.0.1":
  "integrity" "sha512-c9OGXbZ3guC/xOlCg1Ci/VgWlwsqDv1yMQL1CWqXDL0hDjXuNcq0zuR4xqPSuasI3kqFDhqSyTjREz5gzq0fXg=="
  "resolved" "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "lru-cache" "^6.0.0"

"html-tags@^3.1.0":
  "integrity" "sha512-1qYz89hW3lFDEazhjW0yVAV87lw8lVkrJocr72XmBkMKsoSVJCQx3W8BXsC7hO2qAt8BoVjYjtAcZ9perqGnNg=="
  "resolved" "https://registry.npmjs.org/html-tags/-/html-tags-3.1.0.tgz"
  "version" "3.1.0"

"human-signals@^1.1.1":
  "integrity" "sha512-SEQu7vl8KjNL2eoGBLF3+wAjpsNfA9XMlXAYj/3EdaNfAlxKthD1xjEQfGOUhllCGGJVNY34bRr6lPINhNjyZw=="
  "resolved" "https://registry.npmmirror.com/human-signals/-/human-signals-1.1.1.tgz"
  "version" "1.1.1"

"husky@^7.0.2":
  "integrity" "sha512-vbaCKN2QLtP/vD4yvs6iz6hBEo6wkSzs8HpRah1Z6aGmF2KW5PdYuAd7uX5a+OyBZHBhd+TFLqgjUgytQr4RvQ=="
  "resolved" "https://registry.npmjs.org/husky/-/husky-7.0.4.tgz"
  "version" "7.0.4"

"iconv-lite@^0.4.4", "iconv-lite@0.4":
  "integrity" "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"ignore@^4.0.6":
  "integrity" "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-4.0.6.tgz"
  "version" "4.0.6"

"ignore@^5.1.4", "ignore@^5.1.8", "ignore@^5.1.9", "ignore@^5.2.0", "ignore@^5.3.0":
  "integrity" "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g=="
  "resolved" "https://registry.npmmirror.com/ignore/-/ignore-5.3.2.tgz"
  "version" "5.3.2"

"image-size@~0.5.0":
  "integrity" "sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w= sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ=="
  "resolved" "https://registry.npmjs.org/image-size/-/image-size-0.5.5.tgz"
  "version" "0.5.5"

"import-fresh@^3.0.0", "import-fresh@^3.2.1":
  "integrity" "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw=="
  "resolved" "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"import-lazy@^4.0.0":
  "integrity" "sha512-rKtvo6a868b5Hu3heneU+L4yEQ4jYKLtjpnPeUdK7h0yzXGmyBTypknlkCvHFBqfX9YlorEiMM6Dnq/5atfHkw=="
  "resolved" "https://registry.npmjs.org/import-lazy/-/import-lazy-4.0.0.tgz"
  "version" "4.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o= sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA=="
  "resolved" "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^4.0.0":
  "integrity" "sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg=="
  "resolved" "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz"
  "version" "4.0.0"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk= sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA=="
  "resolved" "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@~2.0.4", "inherits@2":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"ini@^1.3.5":
  "integrity" "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew=="
  "resolved" "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz"
  "version" "1.3.8"

"internal-slot@^1.0.3":
  "integrity" "sha512-O0DB1JC/sPyZl7cIo78n5dR7eUSwwpYPiXRhTzNxZVAMUuB8vlnRFyLxdrVToks6XPLVnFfbzaVd5WLjhgg+vA=="
  "resolved" "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "get-intrinsic" "^1.1.0"
    "has" "^1.0.3"
    "side-channel" "^1.0.4"

"internmap@^1.0.0":
  "integrity" "sha512-lDB5YccMydFBtasVtxnZ3MRBHuaoE8GKsppq+EchKL2U4nK/DmEpPHNH8MZe5HkMtpSiTSOZwfN0tzYjO/lJEw=="
  "resolved" "https://registry.npmjs.org/internmap/-/internmap-1.0.1.tgz"
  "version" "1.0.1"

"is-arguments@^1.0.4":
  "integrity" "sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA=="
  "resolved" "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0= sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg=="
  "resolved" "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-arrayish@^0.3.1":
  "integrity" "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ=="
  "resolved" "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz"
  "version" "0.3.2"

"is-bigint@^1.0.1":
  "integrity" "sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg=="
  "resolved" "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-bigints" "^1.0.1"

"is-boolean-object@^1.1.0":
  "integrity" "sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA=="
  "resolved" "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-buffer@^1.1.5":
  "integrity" "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="
  "resolved" "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-callable@^1.1.3", "is-callable@^1.1.4", "is-callable@^1.2.4":
  "integrity" "sha512-nsuwtxZfMX67Oryl9LCQ+upnC0Z0BgpwntpS89m1H/TLF0zNfzfLMV/9Wa/6MZsj0acpEjAO0KF1xT6ZdLl95w=="
  "resolved" "https://registry.npmjs.org/is-callable/-/is-callable-1.2.4.tgz"
  "version" "1.2.4"

"is-core-module@^2.16.0", "is-core-module@^2.2.0", "is-core-module@^2.5.0":
  "integrity" "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w=="
  "resolved" "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.16.1.tgz"
  "version" "2.16.1"
  dependencies:
    "hasown" "^2.0.2"

"is-date-object@^1.0.1":
  "integrity" "sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ=="
  "resolved" "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-extglob@^2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI= sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="
  "resolved" "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@^4.0.3":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-negative-zero@^2.0.1":
  "integrity" "sha512-2z6JzQvZRa9A2Y7xC6dQQm4FSTSTNWjKIYYTt4246eMTJmIo0Q+ZyOsU66X8lxK1AbB92dFeglPLrhwpeRKO6w=="
  "resolved" "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.1.tgz"
  "version" "2.0.1"

"is-number-object@^1.0.4":
  "integrity" "sha512-bEVOqiRcvo3zO1+G2lVMy+gkkEm9Yh7cDMRusKKu5ZJKPUYSJwICTKZrNKHA2EbSP0Tu0+6B/emsYNHZyn6K8g=="
  "resolved" "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-plain-obj@^1.1.0":
  "integrity" "sha1-caUMhCnfync8kqOQpKA7OfzVHT4= sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg=="
  "resolved" "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-object@^5.0.0":
  "integrity" "sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q=="
  "resolved" "https://registry.npmjs.org/is-plain-object/-/is-plain-object-5.0.0.tgz"
  "version" "5.0.0"

"is-regex@^1.0.4", "is-regex@^1.1.4", "is-regex@~1.1.3":
  "integrity" "sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg=="
  "resolved" "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-regexp@^2.0.0":
  "integrity" "sha512-OZ4IlER3zmRIoB9AqNhEggVxqIH4ofDns5nRrPS6yQxXE1TPCUpFznBfRQmQa8uC+pXqjMnukiJBxCisIxiLGA=="
  "resolved" "https://registry.npmjs.org/is-regexp/-/is-regexp-2.1.0.tgz"
  "version" "2.1.0"

"is-shared-array-buffer@^1.0.1":
  "integrity" "sha512-IU0NmyknYZN0rChcKhRO1X8LYz5Isj/Fsqh8NJOSf+N/hCOTwy29F32Ik7a+QszE63IdvmwdTPDd6cZ5pg4cwA=="
  "resolved" "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.1.tgz"
  "version" "1.0.1"

"is-stream@^2.0.0":
  "integrity" "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg=="
  "resolved" "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  "version" "2.0.1"

"is-string@^1.0.5", "is-string@^1.0.7":
  "integrity" "sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg=="
  "resolved" "https://registry.npmjs.org/is-string/-/is-string-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-symbol@^1.0.2", "is-symbol@^1.0.3":
  "integrity" "sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg=="
  "resolved" "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-symbols" "^1.0.2"

"is-typedarray@^1.0.0":
  "integrity" "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo= sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA=="
  "resolved" "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-weakref@^1.0.1":
  "integrity" "sha512-b2jKc2pQZjaeFYWEf7ScFj+Be1I+PXmlu572Q8coTXZ+LD/QQZ7ShPMst8h16riVgyXTQwUsFEl74mDvc/3MHQ=="
  "resolved" "https://registry.npmjs.org/is-weakref/-/is-weakref-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bind" "^1.0.0"

"is-what@^3.12.0":
  "integrity" "sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA=="
  "resolved" "https://registry.npmjs.org/is-what/-/is-what-3.14.1.tgz"
  "version" "3.14.1"

"isarray@~0.0.1", "isarray@0.0.1":
  "integrity" "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8= sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ=="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz"
  "version" "0.0.1"

"isexe@^2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA= sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
  "resolved" "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"jest-worker@^27.4.5":
  "integrity" "sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg=="
  "resolved" "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz"
  "version" "27.5.1"
  dependencies:
    "@types/node" "*"
    "merge-stream" "^2.0.0"
    "supports-color" "^8.0.0"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^3.13.1":
  "integrity" "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g=="
  "resolved" "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"js-yaml@^4.1.0":
  "integrity" "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA=="
  "resolved" "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "argparse" "^2.0.1"

"jsesc@^3.0.2":
  "integrity" "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA=="
  "resolved" "https://registry.npmmirror.com/jsesc/-/jsesc-3.1.0.tgz"
  "version" "3.1.0"

"jsesc@~0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0= sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA=="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-parse-even-better-errors@^2.3.0", "json-parse-even-better-errors@^2.3.1":
  "integrity" "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="
  "resolved" "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema-traverse@^1.0.0":
  "integrity" "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  "version" "1.0.0"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE= sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw=="
  "resolved" "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json2module@^0.0.3":
  "integrity" "sha1-APtfSpt638PwZHwpyxe80Zeb6bI= sha512-qYGxqrRrt4GbB8IEOy1jJGypkNsjWoIMlZt4bAsmUScCA507Hbc2p1JOhBzqn45u3PWafUgH2OnzyNU7udO/GA=="
  "resolved" "https://registry.npmjs.org/json2module/-/json2module-0.0.3.tgz"
  "version" "0.0.3"
  dependencies:
    "rw" "^1.3.2"

"json5@^2.1.2", "json5@^2.2.3":
  "integrity" "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg=="
  "resolved" "https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz"
  "version" "2.2.3"

"jsonfile@^6.0.1":
  "integrity" "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ=="
  "resolved" "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "universalify" "^2.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsx-ast-utils@^2.4.1 || ^3.0.0":
  "integrity" "sha512-uP5vu8xfy2F9A6LGC22KO7e2/vGTS1MhP+18f++ZNlf0Ohaxbc9nIEwHAsejlJKyzfZzU5UIhe5ItYkitcZnZA=="
  "resolved" "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "array-includes" "^3.1.3"
    "object.assign" "^4.1.2"

"kind-of@^3.0.2":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ= sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^6.0.2", "kind-of@^6.0.3":
  "integrity" "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="
  "resolved" "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"klona@^2.0.4":
  "integrity" "sha512-pJiBpiXMbt7dkzXe8Ghj/u4FfXOOa98fPW+bihOJ4SjnoijweJrNThJfd3ifXpXhREjpoF2mZVH1GfS9LV3kHQ=="
  "resolved" "https://registry.npmjs.org/klona/-/klona-2.0.5.tgz"
  "version" "2.0.5"

"known-css-properties@^0.23.0":
  "integrity" "sha512-h9ivI88e1lFNmTT4HovBN33Ysn0OIJG7IPG2mkpx2uniQXFWqo35QdiX7w0TovlUFXfW8aPFblP5/q0jlOr2sA=="
  "resolved" "https://registry.npmjs.org/known-css-properties/-/known-css-properties-0.23.0.tgz"
  "version" "0.23.0"

"lazy-cache@^1.0.3":
  "integrity" "sha1-odePw6UEdMuAhF07O24dpJpEbo4= sha512-RE2g0b5VGZsOCFOCgP7omTRYFqydmZkBwl5oNnQ1lDYC57uyO9KqNnNVxT7COSHTxrRCWVcAVOcbjk+tvh/rgQ=="
  "resolved" "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz"
  "version" "1.0.4"

"less-loader@7.3.0":
  "integrity" "sha512-Mi8915g7NMaLlgi77mgTTQvK022xKRQBIVDSyfl3ErTuBhmZBQab0mjeJjNNqGbdR+qrfTleKXqbGI4uEFavxg=="
  "resolved" "https://registry.npmjs.org/less-loader/-/less-loader-7.3.0.tgz"
  "version" "7.3.0"
  dependencies:
    "klona" "^2.0.4"
    "loader-utils" "^2.0.0"
    "schema-utils" "^3.0.0"

"less@*", "less@^3.5.0 || ^4.0.0", "less@^4.1.2":
  "integrity" "sha512-EoQp/Et7OSOVu0aJknJOtlXZsnr8XE8KwuzTHOLeVSEx8pVWUICc8Q0VYRHgzyjX78nMEyC/oztWFbgyhtNfDA=="
  "resolved" "https://registry.npmjs.org/less/-/less-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "copy-anything" "^2.0.1"
    "parse-node-version" "^1.0.1"
    "tslib" "^2.3.0"
  optionalDependencies:
    "errno" "^0.1.1"
    "graceful-fs" "^4.1.2"
    "image-size" "~0.5.0"
    "make-dir" "^2.1.0"
    "mime" "^1.4.1"
    "needle" "^2.5.2"
    "source-map" "~0.6.0"

"levn@^0.4.1":
  "integrity" "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ=="
  "resolved" "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"lines-and-columns@^1.1.6":
  "integrity" "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="
  "resolved" "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  "version" "1.2.4"

"loader-runner@^4.2.0":
  "integrity" "sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg=="
  "resolved" "https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.0.tgz"
  "version" "4.3.0"

"loader-utils@^2.0.0":
  "integrity" "sha512-TM57VeHptv569d/GKh6TAYdzKblwDNiumOdkFnejjD0XwTH87K90w3O7AiJRqdQoXygvi1VQTJTLGhJl7WqA7A=="
  "resolved" "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^2.1.2"

"locate-path@^5.0.0":
  "integrity" "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"lodash-es@^4.17.15":
  "integrity" "sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw=="
  "resolved" "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz"
  "version" "4.17.21"

"lodash.debounce@^4.0.8":
  "integrity" "sha1-gteb/zCmfEAF/9XiUVMArZyk168= sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow=="
  "resolved" "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.merge@^4.6.2":
  "integrity" "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="
  "resolved" "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.truncate@^4.4.2":
  "integrity" "sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM= sha512-jttmRe7bRse52OsWIMDLaXxWqRAmtIUccAQ3garviCqJjafXOfNMO0yMfNpdD6zbGaTU0P5Nz7e7gAT6cKmJRw=="
  "resolved" "https://registry.npmjs.org/lodash.truncate/-/lodash.truncate-4.4.2.tgz"
  "version" "4.4.2"

"lodash@^4.0.1", "lodash@^4.17.15", "lodash@^4.17.20", "lodash@^4.17.21":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"longest@^1.0.1":
  "integrity" "sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc= sha512-k+yt5n3l48JU4k8ftnKG6V7u32wyH2NfKzeMto9F/QRE0amxy/LayxwlvjjkZEIzqR+19IrtFO8p5kB9QaYUFg=="
  "resolved" "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz"
  "version" "1.0.1"

"loose-envify@^1.0.0", "loose-envify@^1.1.0", "loose-envify@^1.2.0", "loose-envify@^1.3.1", "loose-envify@^1.4.0":
  "integrity" "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  "resolved" "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lru-cache@^5.1.1":
  "integrity" "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w=="
  "resolved" "https://registry.npmmirror.com/lru-cache/-/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"lru-cache@^6.0.0":
  "integrity" "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"make-dir@^2.1.0":
  "integrity" "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA=="
  "resolved" "https://registry.npmjs.org/make-dir/-/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"map-obj@^1.0.0":
  "integrity" "sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0= sha512-7N/q3lyZ+LVCp7PzuxrJr4KMbBE2hW7BT7YNia330OFxIf4d3r5zVpicP2650l7CPN6RM9zOJRl3NGpqSiw3Eg=="
  "resolved" "https://registry.npmjs.org/map-obj/-/map-obj-1.0.1.tgz"
  "version" "1.0.1"

"map-obj@^4.0.0":
  "integrity" "sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ=="
  "resolved" "https://registry.npmjs.org/map-obj/-/map-obj-4.3.0.tgz"
  "version" "4.3.0"

"material-colors@^1.2.1":
  "integrity" "sha512-6qE4B9deFBIa9YSpOc9O0Sgc43zTeVYbgDT5veRKSlB2+ZuHNoVVxA1L/ckMUayV9Ay9y7Z/SZCLcGteW9i7bg=="
  "resolved" "https://registry.npmjs.org/material-colors/-/material-colors-1.2.6.tgz"
  "version" "1.2.6"

"mathml-tag-names@^2.1.3":
  "integrity" "sha512-APMBEanjybaPzUrfqU0IMU5I0AswKMH7k8OTLs0vvV4KZpExkTkY87nR/zpbuTPj+gARop7aGUbl11pnDfW6xg=="
  "resolved" "https://registry.npmjs.org/mathml-tag-names/-/mathml-tag-names-2.1.3.tgz"
  "version" "2.1.3"

"mdn-data@2.0.14":
  "integrity" "sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow=="
  "resolved" "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz"
  "version" "2.0.14"

"mdn-data@2.0.4":
  "integrity" "sha512-iV3XNKw06j5Q7mi6h+9vbx23Tv7JkjEVgKHW4pimwyDGWm0OIQntJJ+u1C6mg6mK1EaTv42XQ7w76yuzH7M2cA=="
  "resolved" "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.4.tgz"
  "version" "2.0.4"

"meow@^9.0.0":
  "integrity" "sha512-+obSblOQmRhcyBt62furQqRAQpNyWXo8BuQ5bN7dG8wmwQ+vwHKp/rCFD4CrTP8CsDQD1sjoZ94K417XEUk8IQ=="
  "resolved" "https://registry.npmjs.org/meow/-/meow-9.0.0.tgz"
  "version" "9.0.0"
  dependencies:
    "@types/minimist" "^1.2.0"
    "camelcase-keys" "^6.2.2"
    "decamelize" "^1.2.0"
    "decamelize-keys" "^1.1.0"
    "hard-rejection" "^2.1.0"
    "minimist-options" "4.1.0"
    "normalize-package-data" "^3.0.0"
    "read-pkg-up" "^7.0.1"
    "redent" "^3.0.0"
    "trim-newlines" "^3.0.0"
    "type-fest" "^0.18.0"
    "yargs-parser" "^20.2.3"

"merge-stream@^2.0.0":
  "integrity" "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w=="
  "resolved" "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge2@^1.3.0":
  "integrity" "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg=="
  "resolved" "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  "version" "1.4.1"

"micromatch@^4.0.2", "micromatch@^4.0.4":
  "integrity" "sha512-pRmzw/XUcwXGpD9aI9q/0XOwLNygjETJ8y0ao0wdqprrzDa4YnxLcz7fQRZr8voh8V10kGhABbNcHVk5wHgWwg=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "braces" "^3.0.1"
    "picomatch" "^2.2.3"

"mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-types@^2.1.27":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mime@^1.4.1":
  "integrity" "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  "version" "1.6.0"

"mimic-fn@^2.1.0":
  "integrity" "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg=="
  "resolved" "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"min-indent@^1.0.0":
  "integrity" "sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg=="
  "resolved" "https://registry.npmjs.org/min-indent/-/min-indent-1.0.1.tgz"
  "version" "1.0.1"

"mini-create-react-context@^0.4.0":
  "integrity" "sha512-YWCYEmd5CQeHGSAKrYvXgmzzkrvssZcuuQDDeqkT+PziKGMgE+0MCCtcKbROzocGBG1meBLl2FotlRwf4gAzbQ=="
  "resolved" "https://registry.npmjs.org/mini-create-react-context/-/mini-create-react-context-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "@babel/runtime" "^7.12.1"
    "tiny-warning" "^1.0.3"

"minimatch@^3.0.4":
  "integrity" "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist-options@4.1.0":
  "integrity" "sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A=="
  "resolved" "https://registry.npmjs.org/minimist-options/-/minimist-options-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "arrify" "^1.0.1"
    "is-plain-obj" "^1.1.0"
    "kind-of" "^6.0.3"

"minimist@^1.2.0", "minimist@^1.2.5", "minimist@~1.2.5":
  "integrity" "sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw=="
  "resolved" "https://registry.npmjs.org/minimist/-/minimist-1.2.5.tgz"
  "version" "1.2.5"

"mkdirp@~0.5.1":
  "integrity" "sha512-NKmAlESf6jMGym1++R0Ra7wvhV+wFW63FaSOFPwRahvea0gMUcGUhVeAg/0BC0wiv9ih5NYPB1Wn1UEI1/L+xQ=="
  "resolved" "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.5.tgz"
  "version" "0.5.5"
  dependencies:
    "minimist" "^1.2.5"

"mockjs@^1.1.0":
  "integrity" "sha512-eQsKcWzIaZzEZ07NuEyO4Nw65g0hdWAyurVol1IPl1gahRwY+svqzfgfey8U8dahLwG44d6/RwEzuK52rSa/JQ=="
  "resolved" "https://registry.npmjs.org/mockjs/-/mockjs-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "commander" "*"

"mri@^1.2.0":
  "integrity" "sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA=="
  "resolved" "https://registry.npmmirror.com/mri/-/mri-1.2.0.tgz"
  "version" "1.2.0"

"ms@^2.1.1", "ms@2.1.2":
  "integrity" "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  "version" "2.1.2"

"nanoid@^3.3.8":
  "integrity" "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w=="
  "resolved" "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.11.tgz"
  "version" "3.3.11"

"natural-compare@^1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc= sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw=="
  "resolved" "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"needle@^2.5.2":
  "integrity" "sha512-6R9fqJ5Zcmf+uYaFgdIHmLwNldn5HbK8L5ybn7Uz+ylX/rnOsSp1AHcvQSrCaFN+qNM1wpymHqD7mVasEOlHGQ=="
  "resolved" "https://registry.npmjs.org/needle/-/needle-2.9.1.tgz"
  "version" "2.9.1"
  dependencies:
    "debug" "^3.2.6"
    "iconv-lite" "^0.4.4"
    "sax" "^1.2.4"

"neo-async@^2.6.2":
  "integrity" "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw=="
  "resolved" "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz"
  "version" "2.6.2"

"node-releases@^2.0.19":
  "integrity" "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw=="
  "resolved" "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz"
  "version" "2.0.19"

"normalize-package-data@^2.5.0":
  "integrity" "sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA=="
  "resolved" "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-package-data@^3.0.0":
  "integrity" "sha512-p2W1sgqij3zMMyRC067Dg16bfzVH+w7hyegmpIvZ4JNjqtGOVAIvLmjBx3yP7YTe9vKJgkoNOPjwQGogDoMXFA=="
  "resolved" "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "hosted-git-info" "^4.0.1"
    "is-core-module" "^2.5.0"
    "semver" "^7.3.4"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-selector@^0.2.0":
  "integrity" "sha1-0LFF62kRicY6eNIB3E/bEpPvDAM= sha512-dxvWdI8gw6eAvk9BlPffgEoGfM7AdijoCwOEJge3e3ulT2XLgmU7KvvxprOaCu05Q1uGRHmOhHe1r6emZoKyFw=="
  "resolved" "https://registry.npmjs.org/normalize-selector/-/normalize-selector-0.2.0.tgz"
  "version" "0.2.0"

"npm-run-path@^4.0.0":
  "integrity" "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw=="
  "resolved" "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"nprogress@^0.2.0":
  "integrity" "sha1-y480xTIT2JVyP8urkH6UIq28r7E= sha512-I19aIingLgR1fmhftnbWWO3dXc0hSxqHQHQb3H8m+K3TnEn/iSeTZZOyvKXWqQESMwuUVnatlCnZdLBZZt2VSA=="
  "resolved" "https://registry.npmjs.org/nprogress/-/nprogress-0.2.0.tgz"
  "version" "0.2.0"

"nth-check@^1.0.2":
  "integrity" "sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg=="
  "resolved" "https://registry.npmjs.org/nth-check/-/nth-check-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "boolbase" "~1.0.0"

"number-precision@^1.3.1":
  "integrity" "sha512-JdTfhUHd1aUL4svSaJXlv3CKNz92js2z0+HS/9AX8euw+akompBz/QEZ7e3y7eTuJxvRqXBZP6mapUlTtLoTsA=="
  "resolved" "https://registry.npmjs.org/number-precision/-/number-precision-1.5.1.tgz"
  "version" "1.5.1"

"object-assign@*", "object-assign@^4.1.1":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM= sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="
  "resolved" "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-inspect@^1.11.0", "object-inspect@^1.9.0", "object-inspect@~1.11.0":
  "integrity" "sha512-jp7ikS6Sd3GxQfZJPyH3cjcbJF6GZPClgdV+EFygjFLQ5FmW/dRUnTd9PQ9k0JhoNDabWFbpF1yCdSWCC6gexg=="
  "resolved" "https://registry.npmjs.org/object-inspect/-/object-inspect-1.11.0.tgz"
  "version" "1.11.0"

"object-is@^1.0.1":
  "integrity" "sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw=="
  "resolved" "https://registry.npmjs.org/object-is/-/object-is-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"object-keys@^1.0.12", "object-keys@^1.1.1":
  "integrity" "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA=="
  "resolved" "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object.assign@^4.1.0", "object.assign@^4.1.2":
  "integrity" "sha512-ixT2L5THXsApyiUPYKmW+2EHpXXe5Ii3M+f4e+aJFAHao5amFRW6J0OO6c/LU8Be47utCx2GL89hxGB6XSmKuQ=="
  "resolved" "https://registry.npmjs.org/object.assign/-/object.assign-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "has-symbols" "^1.0.1"
    "object-keys" "^1.1.1"

"object.entries@^1.1.5":
  "integrity" "sha512-TyxmjUoZggd4OrrU1W66FMDG6CuqJxsFvymeyXI51+vQLN67zYfZseptRge703kKQdo4uccgAKebXFcRCzk4+g=="
  "resolved" "https://registry.npmjs.org/object.entries/-/object.entries-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"object.fromentries@^2.0.5":
  "integrity" "sha512-CAyG5mWQRRiBU57Re4FKoTBjXfDoNwdFVH2Y1tS9PqCsfUTymAohOkEMSG3aRNKmv4lV3O7p1et7c187q6bynw=="
  "resolved" "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"object.getownpropertydescriptors@^2.1.0":
  "integrity" "sha512-VdDoCwvJI4QdC6ndjpqFmoL3/+HxffFBbcJzKi5hwLLqqx3mdbedRpfZDdK0SrOSauj8X4GzBvnDZl4vTN7dOw=="
  "resolved" "https://registry.npmjs.org/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"object.hasown@^1.1.0":
  "integrity" "sha512-MhjYRfj3GBlhSkDHo6QmvgjRLXQ2zndabdf3nX0yTyZK9rPfxb6uRpAac8HXNLy1GpqWtZ81Qh4v3uOls2sRAg=="
  "resolved" "https://registry.npmjs.org/object.hasown/-/object.hasown-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"object.values@^1.1.0", "object.values@^1.1.5":
  "integrity" "sha512-QUZRW0ilQ3PnPpbNtgdNV1PDbEqLIiSFB3l+EnGtBQ/8SUTLj1PZwtQHABZtLgwpJZTSZhuGLOGk57Drx2IvYg=="
  "resolved" "https://registry.npmjs.org/object.values/-/object.values-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E= sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w=="
  "resolved" "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^5.1.0":
  "integrity" "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg=="
  "resolved" "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"optionator@^0.9.1":
  "integrity" "sha512-74RlY5FCnhq4jRxVUPKDaRwrVNXMqsGsiW6AJw4XK8hmtm10wC0ypZBLw5IIp85NZMr91+qd1RvvENwg7jjRFw=="
  "resolved" "https://registry.npmjs.org/optionator/-/optionator-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"
    "word-wrap" "^1.2.3"

"p-limit@^2.2.0":
  "integrity" "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-try@^2.0.0":
  "integrity" "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="
  "resolved" "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  "version" "2.2.0"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-json@^5.0.0":
  "integrity" "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg=="
  "resolved" "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-even-better-errors" "^2.3.0"
    "lines-and-columns" "^1.1.6"

"parse-node-version@^1.0.1":
  "integrity" "sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA=="
  "resolved" "https://registry.npmjs.org/parse-node-version/-/parse-node-version-1.0.1.tgz"
  "version" "1.0.1"

"parse-svg-path@~0.1.1":
  "integrity" "sha1-en7A0esG+lMlx9PgCbhZoJtdSes= sha512-JyPSBnkTJ0AI8GGJLfMXvKq42cj5c006fnLz6fXy6zfoVjJizi8BNTpu8on8ziI1cKy9d9DGNuY17Ce7wuejpQ=="
  "resolved" "https://registry.npmjs.org/parse-svg-path/-/parse-svg-path-0.1.2.tgz"
  "version" "0.1.2"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18= sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg=="
  "resolved" "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-key@^3.0.0", "path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.6", "path-parse@^1.0.7":
  "integrity" "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="
  "resolved" "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-to-regexp@^1.7.0":
  "integrity" "sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA=="
  "resolved" "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "isarray" "0.0.1"

"path-type@^4.0.0":
  "integrity" "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw=="
  "resolved" "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  "version" "4.0.0"

"pdfast@^0.2.0":
  "integrity" "sha1-jLxVbhvyUiF3eHwN4uDUNzuohck= sha512-cq6TTu6qKSFUHwEahi68k/kqN2mfepjkGrG9Un70cgdRRKLKY6Rf8P8uvP2NvZktaQZNF3YE7agEkLj0vGK9bA=="
  "resolved" "https://registry.npmjs.org/pdfast/-/pdfast-0.2.0.tgz"
  "version" "0.2.0"

"picocolors@^1.0.0", "picocolors@^1.1.1":
  "integrity" "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="
  "resolved" "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  "version" "1.1.1"

"picomatch@^2.2.2", "picomatch@^2.2.3":
  "integrity" "sha512-lY1Q/PiJGC2zOv/z391WOTD+Z02bCgsFfvxoXXf6h7kv9o+WmsmzYqrAwY63sNgOxE4xEdq0WyUnXfKeBrSvYw=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.0.tgz"
  "version" "2.3.0"

"picomatch@^3.0.1":
  "integrity" "sha512-I3EurrIQMlRc9IaAZnqRR044Phh2DXY+55o7uJ0V+hYZAcQYSuFWsc9q5PvyDHUSCe1Qxn/iBz+78s86zWnGag=="
  "resolved" "https://registry.npmmirror.com/picomatch/-/picomatch-3.0.1.tgz"
  "version" "3.0.1"

"pify@^4.0.1":
  "integrity" "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g=="
  "resolved" "https://registry.npmjs.org/pify/-/pify-4.0.1.tgz"
  "version" "4.0.1"

"point-at-length@^1.0.2":
  "integrity" "sha1-CtcuvQmA1/WhqxIpbAVfnrazDlc= sha512-nNHDk9rNEh/91o2Y8kHLzBLNpLf80RYd2gCun9ss+V0ytRSf6XhryBTx071fesktjbachRmGuUbId+JQmzhRXw=="
  "resolved" "https://registry.npmjs.org/point-at-length/-/point-at-length-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "abs-svg-path" "~0.1.1"
    "isarray" "~0.0.1"
    "parse-svg-path" "~0.1.1"

"point-in-polygon@^1.1.0":
  "integrity" "sha512-3ojrFwjnnw8Q9242TzgXuTD+eKiutbzyslcq1ydfu82Db2y+Ogbmyrkpv0Hgj31qwT3lbS9+QAAO/pIQM35XRw=="
  "resolved" "https://registry.npmjs.org/point-in-polygon/-/point-in-polygon-1.1.0.tgz"
  "version" "1.1.0"

"polygon-clipping@^0.15.3":
  "integrity" "sha512-ho0Xx5DLkgxRx/+n4O74XyJ67DcyN3Tu9bGYKsnTukGAW6ssnuak6Mwcyb1wHy9MZc9xsUWqIoiazkZB5weECg=="
  "resolved" "https://registry.npmjs.org/polygon-clipping/-/polygon-clipping-0.15.3.tgz"
  "version" "0.15.3"
  dependencies:
    "splaytree" "^3.1.0"

"postcss-less@4":
  "integrity" "sha512-C92S4sHlbDpefJ2QQJjrucCcypq3+KZPstjfuvgOCNnGx0tF9h8hXgAlOIATGAxMXZXaF+nVp+/Mi8pCAWdSmw=="
  "resolved" "https://registry.npmjs.org/postcss-less/-/postcss-less-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^8.1.2"

"postcss-media-query-parser@^0.2.3":
  "integrity" "sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ= sha512-3sOlxmbKcSHMjlUXQZKQ06jOswE7oVkXPxmZdoB1r5l0q6gTFTQSHxNxOrCccElbW7dxNytifNEo8qidX2Vsig=="
  "resolved" "https://registry.npmjs.org/postcss-media-query-parser/-/postcss-media-query-parser-0.2.3.tgz"
  "version" "0.2.3"

"postcss-resolve-nested-selector@^0.1.1":
  "integrity" "sha1-Kcy8fDfe36wwTp//C/FZaz9qDk4= sha512-HvExULSwLqHLgUy1rl3ANIqCsvMS0WHss2UOsXhXnQaZ9VCc2oBvIpXrl00IUFT5ZDITME0o6oiXeiHr2SAIfw=="
  "resolved" "https://registry.npmjs.org/postcss-resolve-nested-selector/-/postcss-resolve-nested-selector-0.1.1.tgz"
  "version" "0.1.1"

"postcss-safe-parser@^6.0.0":
  "integrity" "sha512-FARHN8pwH+WiS2OPCxJI8FuRJpTVnn6ZNFiqAM2aeW2LwTHWWmWgIyKC6cUo0L8aeKiF/14MNvnpls6R2PBeMQ=="
  "resolved" "https://registry.npmjs.org/postcss-safe-parser/-/postcss-safe-parser-6.0.0.tgz"
  "version" "6.0.0"

"postcss-selector-parser@^6.0.6":
  "integrity" "sha512-9LXrvaaX3+mcv5xkg5kFwqSzSH1JIObIx51PrndZwlmznwXRfxMddDvo9gve3gVR8ZTKgoFDdWkbRFmEhT4PMg=="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.6.tgz"
  "version" "6.0.6"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-value-parser@^4.1.0":
  "integrity" "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ=="
  "resolved" "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  "version" "4.2.0"

"postcss@^8.1.2", "postcss@^8.3.11", "postcss@^8.3.3", "postcss@^8.4.13":
  "integrity" "sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A=="
  "resolved" "https://registry.npmmirror.com/postcss/-/postcss-8.5.3.tgz"
  "version" "8.5.3"
  dependencies:
    "nanoid" "^3.3.8"
    "picocolors" "^1.1.1"
    "source-map-js" "^1.2.1"

"prelude-ls@^1.2.1":
  "integrity" "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g=="
  "resolved" "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"prettier-linter-helpers@^1.0.0":
  "integrity" "sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w=="
  "resolved" "https://registry.npmjs.org/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "fast-diff" "^1.1.2"

"prettier@^2.0.0", "prettier@^2.4.1", "prettier@>=2.0.0":
  "integrity" "sha512-FM/zAKgWTxj40rH03VxzIPdXmj39SwSjwG0heUcNFwI+EMZJnY93yAiKXM3dObIKAM5TA88werc8T/EwhB45eg=="
  "resolved" "https://registry.npmjs.org/prettier/-/prettier-2.5.0.tgz"
  "version" "2.5.0"

"pretty-quick@^3.1.2":
  "integrity" "sha512-3b36UXfYQ+IXXqex6mCca89jC8u0mYLqFAN5eTQKoXO6oCQYcIVYZEB/5AlBHI7JPYygReM2Vv6Vom/Gln7fBg=="
  "resolved" "https://registry.npmmirror.com/pretty-quick/-/pretty-quick-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "execa" "^4.1.0"
    "find-up" "^4.1.0"
    "ignore" "^5.3.0"
    "mri" "^1.2.0"
    "picocolors" "^1.0.0"
    "picomatch" "^3.0.1"
    "tslib" "^2.6.2"

"prop-types@^15.0.0", "prop-types@^15.5.10", "prop-types@^15.6.2", "prop-types@^15.7.2":
  "integrity" "sha512-8QQikdH7//R2vurIJSutZ1smHYTcLpRWEOlHnzcWHmBYrOGUysKwSsrC89BCiFj3CbrfJ/nXFdJepOVrY1GCHQ=="
  "resolved" "https://registry.npmjs.org/prop-types/-/prop-types-15.7.2.tgz"
  "version" "15.7.2"
  dependencies:
    "loose-envify" "^1.4.0"
    "object-assign" "^4.1.1"
    "react-is" "^16.8.1"

"prr@~1.0.1":
  "integrity" "sha1-0/wRS6BplaRexok/SEzrHXj19HY= sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw=="
  "resolved" "https://registry.npmjs.org/prr/-/prr-1.0.1.tgz"
  "version" "1.0.1"

"pump@^3.0.0":
  "integrity" "sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw=="
  "resolved" "https://registry.npmmirror.com/pump/-/pump-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"punycode@^2.1.0":
  "integrity" "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz"
  "version" "2.1.1"

"q@^1.1.2":
  "integrity" "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc= sha512-kV/CThkXo6xyFEZUugw/+pIOywXcDbFYgSct5cT3gqlbkBE1SJdwy6UQoZvodiWF/ckQLZyDE/Bu1M6gVu5lVw=="
  "resolved" "https://registry.npmjs.org/q/-/q-1.5.1.tgz"
  "version" "1.5.1"

"query-string@^6.13.8":
  "integrity" "sha512-XDxAeVmpfu1/6IjyT/gXHOl+S0vQ9owggJ30hhWKdHAsNPOcasn5o9BW0eejZqL2e4vMjhAxoW3jVHcD6mbcYw=="
  "resolved" "https://registry.npmjs.org/query-string/-/query-string-6.14.1.tgz"
  "version" "6.14.1"
  dependencies:
    "decode-uri-component" "^0.2.0"
    "filter-obj" "^1.1.0"
    "split-on-first" "^1.0.0"
    "strict-uri-encode" "^2.0.0"

"queue-microtask@^1.2.2":
  "integrity" "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A=="
  "resolved" "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"quick-lru@^4.0.1":
  "integrity" "sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g=="
  "resolved" "https://registry.npmjs.org/quick-lru/-/quick-lru-4.0.1.tgz"
  "version" "4.0.1"

"quickselect@^1.0.1":
  "integrity" "sha512-qN0Gqdw4c4KGPsBOQafj6yj/PA6c/L63f6CaZ/DCF/xF4Esu3jVmKLUDYxghFx8Kb/O7y9tI7x2RjTSXwdK1iQ=="
  "resolved" "https://registry.npmjs.org/quickselect/-/quickselect-1.1.1.tgz"
  "version" "1.1.1"

"quickselect@^2.0.0":
  "integrity" "sha512-RKJ22hX8mHe3Y6wH/N3wCM6BWtjaxIyyUIkpHOvfFnxdI4yD4tBXEBKSbriGujF6jnSVkJrffuo6vxACiSSxIw=="
  "resolved" "https://registry.npmjs.org/quickselect/-/quickselect-2.0.0.tgz"
  "version" "2.0.0"

"randombytes@^2.1.0":
  "integrity" "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ=="
  "resolved" "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"rbush@^2.0.1", "rbush@2.x":
  "integrity" "sha512-XBOuALcTm+O/H8G90b6pzu6nX6v2zCKiFG4BJho8a+bY6AER6t8uQUZdi5bomQc0AprCWhEGa7ncAbbRap0bRA=="
  "resolved" "https://registry.npmjs.org/rbush/-/rbush-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "quickselect" "^1.0.1"

"rbush@^3.0.1":
  "integrity" "sha512-XRaVO0YecOpEuIvbhbpTrZgoiI6xBlz6hnlr6EHhd+0x9ase6EmeN+hdwwUaJvLcsFFQ8iWVF1GAK1yB0BWi0w=="
  "resolved" "https://registry.npmjs.org/rbush/-/rbush-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "quickselect" "^2.0.0"

"react-clientside-effect@^1.2.6":
  "integrity" "sha512-gce9m0Pk/xYYMEojRI9bgvqQAkl6hm7ozQvqWPyQx+kULiatdHgkNM1QG4DQRx5N9BAzWSCJmt9mMV8/KsdgVg=="
  "resolved" "https://registry.npmmirror.com/react-clientside-effect/-/react-clientside-effect-1.2.7.tgz"
  "version" "1.2.7"
  dependencies:
    "@babel/runtime" "^7.12.13"

"react-color@^2.18.1":
  "integrity" "sha512-LEeGE/ZzNLIsFWa1TMe8y5VYqr7bibneWmvJwm1pCn/eNmrabWDh659JSPn9BuaMpEfU83WTOJfnCcjDZwNQTA=="
  "resolved" "https://registry.npmjs.org/react-color/-/react-color-2.19.3.tgz"
  "version" "2.19.3"
  dependencies:
    "@icons/material" "^0.2.4"
    "lodash" "^4.17.15"
    "lodash-es" "^4.17.15"
    "material-colors" "^1.2.1"
    "prop-types" "^15.5.10"
    "reactcss" "^1.2.0"
    "tinycolor2" "^1.4.1"

"react-dom@^17.0.2", "react-dom@>=16", "react-dom@>=16.6.0":
  "integrity" "sha512-s4h96KtLDUQlsENhMn1ar8t2bEa+q/YAtj8pPPdIjPDGBDIVNsrD9aXNWqspUe6AzKCIG0C1HZZLqLV7qpOBGA=="
  "resolved" "https://registry.npmjs.org/react-dom/-/react-dom-17.0.2.tgz"
  "version" "17.0.2"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"
    "scheduler" "^0.20.2"

"react-error-boundary@3.0.2":
  "integrity" "sha512-KVzCusRTFpUYG0OFJbzbdRuxNQOBiGXVCqyNpBXM9z5NFsFLzMjUXMjx8gTja6M6WH+D2PvP3yKz4d8gD1PRaA=="
  "resolved" "https://registry.npmjs.org/react-error-boundary/-/react-error-boundary-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "@babel/runtime" "^7.11.2"

"react-focus-lock@^2.13.2":
  "integrity" "sha512-HjHuZFFk2+j6ZT3LDQpyqffue541HrxUG/OFchCEwis9nstgNg0rREVRAxHBcB1lHJ5Fsxtx1qya/5xFwxDb4g=="
  "resolved" "https://registry.npmmirror.com/react-focus-lock/-/react-focus-lock-2.13.5.tgz"
  "version" "2.13.5"
  dependencies:
    "@babel/runtime" "^7.0.0"
    "focus-lock" "^1.3.5"
    "prop-types" "^15.6.2"
    "react-clientside-effect" "^1.2.6"
    "use-callback-ref" "^1.3.2"
    "use-sidecar" "^1.1.2"

"react-is@^16.12.0":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-is@^16.6.0":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-is@^16.7.0":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-is@^16.8.1":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-is@^17.0.2":
  "integrity" "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz"
  "version" "17.0.2"

"react-is@^18.2.0":
  "integrity" "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg=="
  "resolved" "https://registry.npmmirror.com/react-is/-/react-is-18.3.1.tgz"
  "version" "18.3.1"

"react-reconciler@^0.25.1":
  "integrity" "sha512-R5UwsIvRcSs3w8n9k3tBoTtUHdVhu9u84EG7E5M0Jk9F5i6DA1pQzPfUZd6opYWGy56MJOtV3VADzy6DRwYDjw=="
  "resolved" "https://registry.npmjs.org/react-reconciler/-/react-reconciler-0.25.1.tgz"
  "version" "0.25.1"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"
    "prop-types" "^15.6.2"
    "scheduler" "^0.19.1"

"react-redux@^7.2.6":
  "integrity" "sha512-10RPdsz0UUrRL1NZE0ejTkucnclYSgXp5q+tB5SWx2qeG2ZJQJyymgAhwKy73yiL/13btfB6fPr+rgbMAaZIAQ=="
  "resolved" "https://registry.npmjs.org/react-redux/-/react-redux-7.2.6.tgz"
  "version" "7.2.6"
  dependencies:
    "@babel/runtime" "^7.15.4"
    "@types/react-redux" "^7.1.20"
    "hoist-non-react-statics" "^3.3.2"
    "loose-envify" "^1.4.0"
    "prop-types" "^15.7.2"
    "react-is" "^17.0.2"

"react-refresh@^0.13.0":
  "integrity" "sha512-XP8A9BT0CpRBD+NYLLeIhld/RqG9+gktUjW1FkE+Vm7OCinbG1SshcK5tb9ls4kzvjZr9mOQc7HYgBngEyPAXg=="
  "resolved" "https://registry.npmmirror.com/react-refresh/-/react-refresh-0.13.0.tgz"
  "version" "0.13.0"

"react-router-dom@^5.2.0":
  "integrity" "sha512-ObVBLjUZsphUUMVycibxgMdh5jJ1e3o+KpAZBVeHcNQZ4W+uUGGWsokurzlF4YOldQYRQL4y6yFRWM4m3svmuQ=="
  "resolved" "https://registry.npmjs.org/react-router-dom/-/react-router-dom-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "@babel/runtime" "^7.12.13"
    "history" "^4.9.0"
    "loose-envify" "^1.3.1"
    "prop-types" "^15.6.2"
    "react-router" "5.2.1"
    "tiny-invariant" "^1.0.2"
    "tiny-warning" "^1.0.0"

"react-router@^5.2.0", "react-router@5.2.1":
  "integrity" "sha512-lIboRiOtDLFdg1VTemMwud9vRVuOCZmUIT/7lUoZiSpPODiiH1UQlfXy+vPLC/7IWdFYnhRwAyNqA/+I7wnvKQ=="
  "resolved" "https://registry.npmjs.org/react-router/-/react-router-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "@babel/runtime" "^7.12.13"
    "history" "^4.9.0"
    "hoist-non-react-statics" "^3.1.0"
    "loose-envify" "^1.3.1"
    "mini-create-react-context" "^0.4.0"
    "path-to-regexp" "^1.7.0"
    "prop-types" "^15.6.2"
    "react-is" "^16.6.0"
    "tiny-invariant" "^1.0.2"
    "tiny-warning" "^1.0.0"

"react-transition-group@^4.3.0":
  "integrity" "sha512-/RNYfRAMlZwDSr6z4zNKV6xu53/e2BuaBbGhbyYIXTrmgu/bGHzmqOs7mJSJBHy9Ud+ApHx3QjrkKSp1pxvlFg=="
  "resolved" "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.2.tgz"
  "version" "4.4.2"
  dependencies:
    "@babel/runtime" "^7.5.5"
    "dom-helpers" "^5.0.1"
    "loose-envify" "^1.4.0"
    "prop-types" "^15.6.2"

"react@*", "react@^0.14.0 || ^15.0.0 || ^16.0.0 || ^17.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react@^16.8.3 || ^17", "react@^17.0.2", "react@>=15", "react@>=16", "react@>=16.13.1", "react@>=16.3.0", "react@>=16.6.0", "react@17.0.2":
  "integrity" "sha512-gnhPt75i/dq/z3/6q/0asP78D0u592D5L1pd7M8P+dck6Fu/jJeL6iVVK23fptSUZj8Vjf++7wXA8UNclGQcbA=="
  "resolved" "https://registry.npmjs.org/react/-/react-17.0.2.tgz"
  "version" "17.0.2"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"

"react@^16.13.1":
  "integrity" "sha512-0X2CImDkJGApiAlcf0ODKIneSwBPhqJawOa5wCtKbu7ZECrmS26NvtSILynQ66cgkT/RJ4LidJOc3bUESwmU8g=="
  "resolved" "https://registry.npmjs.org/react/-/react-16.14.0.tgz"
  "version" "16.14.0"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"
    "prop-types" "^15.6.2"

"reactcss@^1.2.0":
  "integrity" "sha512-KiwVUcFu1RErkI97ywr8nvx8dNOpT03rbnma0SSalTYjkrPYaEajR4a/MRt6DZ46K6arDRbWMNHF+xH7G7n/8A=="
  "resolved" "https://registry.npmjs.org/reactcss/-/reactcss-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "lodash" "^4.0.1"

"read-pkg-up@^7.0.1":
  "integrity" "sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg=="
  "resolved" "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "find-up" "^4.1.0"
    "read-pkg" "^5.2.0"
    "type-fest" "^0.8.1"

"read-pkg@^5.2.0":
  "integrity" "sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg=="
  "resolved" "https://registry.npmjs.org/read-pkg/-/read-pkg-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    "normalize-package-data" "^2.5.0"
    "parse-json" "^5.0.0"
    "type-fest" "^0.6.0"

"redent@^3.0.0":
  "integrity" "sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg=="
  "resolved" "https://registry.npmjs.org/redent/-/redent-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "indent-string" "^4.0.0"
    "strip-indent" "^3.0.0"

"redux@^4.0.0", "redux@^4.1.2":
  "integrity" "sha512-SH8PglcebESbd/shgf6mii6EIoRM0zrQyjcuQ+ojmfxjTtE0z9Y8pa62iA/OJ58qjP6j27uyW4kUF4jl/jd6sw=="
  "resolved" "https://registry.npmjs.org/redux/-/redux-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "@babel/runtime" "^7.9.2"

"regenerate-unicode-properties@^9.0.0":
  "integrity" "sha512-3E12UeNSPfjrgwjkR81m5J7Aw/T55Tu7nUyZVQYCKEOs+2dkxEY+DpPtZzO4YruuiPb7NkYLVcyJC4+zCbk5pA=="
  "resolved" "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-9.0.0.tgz"
  "version" "9.0.0"
  dependencies:
    "regenerate" "^1.4.2"

"regenerate@^1.4.2":
  "integrity" "sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A=="
  "resolved" "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.13.4":
  "integrity" "sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz"
  "version" "0.13.9"

"regenerator-transform@^0.14.2":
  "integrity" "sha512-eOf6vka5IO151Jfsw2NO9WpGX58W6wWmefK3I1zEGr0lOD0u8rwPaNqQL1aRxUaxLeKO3ArNh3VYg1KbaD+FFw=="
  "resolved" "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.14.5.tgz"
  "version" "0.14.5"
  dependencies:
    "@babel/runtime" "^7.8.4"

"regexp.prototype.flags@^1.2.0", "regexp.prototype.flags@^1.3.1":
  "integrity" "sha512-JiBdRBq91WlY7uRJ0ds7R+dU02i6LKi8r3BuQhNXn+kmeLN+EfHhfjqMRis1zJxnlu88hq/4dx0P2OP3APRTOA=="
  "resolved" "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"regexpp@^3.2.0":
  "integrity" "sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg=="
  "resolved" "https://registry.npmjs.org/regexpp/-/regexpp-3.2.0.tgz"
  "version" "3.2.0"

"regexpu-core@^4.7.1":
  "integrity" "sha512-1F6bYsoYiz6is+oz70NWur2Vlh9KWtswuRuzJOfeYUrfPX2o8n74AnUVaOGDbUqVGO9fNHu48/pjJO4sNVwsOg=="
  "resolved" "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.8.0.tgz"
  "version" "4.8.0"
  dependencies:
    "regenerate" "^1.4.2"
    "regenerate-unicode-properties" "^9.0.0"
    "regjsgen" "^0.5.2"
    "regjsparser" "^0.7.0"
    "unicode-match-property-ecmascript" "^2.0.0"
    "unicode-match-property-value-ecmascript" "^2.0.0"

"regjsgen@^0.5.2":
  "integrity" "sha512-OFFT3MfrH90xIW8OOSyUrk6QHD5E9JOTeGodiJeBS3J6IwlgzJMNE/1bZklWz5oTg+9dCMyEetclvCVXOPoN3A=="
  "resolved" "https://registry.npmjs.org/regjsgen/-/regjsgen-0.5.2.tgz"
  "version" "0.5.2"

"regjsparser@^0.7.0":
  "integrity" "sha512-A4pcaORqmNMDVwUjWoTzuhwMGpP+NykpfqAsEgI1FSH/EzC7lrN5TMd+kN8YCovX+jMpu8eaqXgXPCa0g8FQNQ=="
  "resolved" "https://registry.npmjs.org/regjsparser/-/regjsparser-0.7.0.tgz"
  "version" "0.7.0"
  dependencies:
    "jsesc" "~0.5.0"

"regression@^2.0.0":
  "integrity" "sha1-jSnD6CJKEIUMNeM36FqLL6w7DIc= sha512-A4XYsc37dsBaNOgEjkJKzfJlE394IMmUPlI/p3TTI9u3T+2a+eox5Pr/CPUqF0eszeWZJPAc6QkroAhuUpWDJQ=="
  "resolved" "https://registry.npmjs.org/regression/-/regression-2.0.1.tgz"
  "version" "2.0.1"

"repeat-string@^1.5.2":
  "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc= sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w=="
  "resolved" "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"require-from-string@^2.0.2":
  "integrity" "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="
  "resolved" "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"resize-observer-polyfill@^1.5.1":
  "integrity" "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="
  "resolved" "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-from@^5.0.0":
  "integrity" "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz"
  "version" "5.0.0"

"resolve-pathname@^3.0.0":
  "integrity" "sha512-C7rARubxI8bXFNB/hqcp/4iUeIXJhJZvFPFPiSPRnhU5UPxzMFIl+2E6yY6c4k9giDJAhtV+enfA+G89N6Csng=="
  "resolved" "https://registry.npmjs.org/resolve-pathname/-/resolve-pathname-3.0.0.tgz"
  "version" "3.0.0"

"resolve@^1.10.0", "resolve@^1.14.2", "resolve@~1.20.0":
  "integrity" "sha512-wENBPt4ySzg4ybFQW2TT1zMQucPK95HSh/nq2CFTZVOGut2+pQvSsgtda4d26YrYcr067wjbmzOG8byDPBX63A=="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-1.20.0.tgz"
  "version" "1.20.0"
  dependencies:
    "is-core-module" "^2.2.0"
    "path-parse" "^1.0.6"

"resolve@^1.22.0":
  "integrity" "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w=="
  "resolved" "https://registry.npmmirror.com/resolve/-/resolve-1.22.10.tgz"
  "version" "1.22.10"
  dependencies:
    "is-core-module" "^2.16.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"resolve@^2.0.0-next.3":
  "integrity" "sha512-W8LucSynKUIDu9ylraa7ueVZ7hc0uAgJBxVsQSKOXOyle8a93qXhcz+XAXZ8bIq2d6i4Ehddn6Evt+0/UwKk6Q=="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.3.tgz"
  "version" "2.0.0-next.3"
  dependencies:
    "is-core-module" "^2.2.0"
    "path-parse" "^1.0.6"

"resumer@~0.0.0":
  "integrity" "sha1-8ej0YeQGS6Oegq883CqMiT0HZ1k= sha512-Fn9X8rX8yYF4m81rZCK/5VmrmsSbqS/i3rDLl6ZZHAXgC2nTAx3dhwG8q8odP/RmdLa2YrybDJaAMg+X1ajY3w=="
  "resolved" "https://registry.npmjs.org/resumer/-/resumer-0.0.0.tgz"
  "version" "0.0.0"
  dependencies:
    "through" "~2.3.4"

"reusify@^1.0.4":
  "integrity" "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw=="
  "resolved" "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  "version" "1.0.4"

"right-align@^0.1.1":
  "integrity" "sha1-YTObci/mo1FWiSENJOFMlhSGE+8= sha512-yqINtL/G7vs2v+dFIZmFUDbnVyFUJFKd6gK22Kgo6R4jfJGFtisKyncWDDULgjfqf4ASQuIQyjJ7XZ+3aWpsAg=="
  "resolved" "https://registry.npmjs.org/right-align/-/right-align-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "align-text" "^0.1.1"

"rimraf@^3.0.2":
  "integrity" "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA=="
  "resolved" "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"robust-predicates@^2.0.4":
  "integrity" "sha512-l4NwboJM74Ilm4VKfbAtFeGq7aEjWL+5kVFcmgFA2MrdnQWx9iE/tUGvxY5HyMI7o/WpSIUFLbC5fbeaHgSCYg=="
  "resolved" "https://registry.npmjs.org/robust-predicates/-/robust-predicates-2.0.4.tgz"
  "version" "2.0.4"

"rollup@^0.25.8":
  "integrity" "sha1-v2zoO4dRDRY0Ru6qV37WpvxYNeA= sha512-a2S4Bh3bgrdO4BhKr2E4nZkjTvrJ2m2bWjMTzVYtoqSCn0HnuxosXnaJUHrMEziOWr3CzL9GjilQQKcyCQpJoA=="
  "resolved" "https://registry.npmjs.org/rollup/-/rollup-0.25.8.tgz"
  "version" "0.25.8"
  dependencies:
    "chalk" "^1.1.1"
    "minimist" "^1.2.0"
    "source-map-support" "^0.3.2"

"rollup@>=2.59.0 <2.78.0":
  "integrity" "sha512-1Bgjpq61sPjgoZzuiDSGvbI1tD91giZABgjCQBKM5aYLnzjq52GoDuWVwT/cm/MCxCMPU8gqQvkj8doQ5C8Oqw=="
  "resolved" "https://registry.npmjs.org/rollup/-/rollup-2.60.2.tgz"
  "version" "2.60.2"
  optionalDependencies:
    "fsevents" "~2.3.2"

"run-parallel@^1.1.9":
  "integrity" "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA=="
  "resolved" "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"rw@^1.3.2", "rw@1":
  "integrity" "sha1-P4Yt+pGrdmsUiF700BEkv9oHT7Q= sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ=="
  "resolved" "https://registry.npmjs.org/rw/-/rw-1.3.3.tgz"
  "version" "1.3.3"

"safe-buffer@^5.1.0":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safer-buffer@>= 2.1.2 < 3":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sax@^1.2.4", "sax@~1.2.4":
  "integrity" "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="
  "resolved" "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz"
  "version" "1.2.4"

"scheduler@^0.19.1":
  "integrity" "sha512-n/zwRWRYSUj0/3g/otKDRPMh6qv2SYMWNq85IEa8iZyAv8od9zDYpGSnpBEjNgcMNq6Scbu5KfIPxNF72R/2EA=="
  "resolved" "https://registry.npmjs.org/scheduler/-/scheduler-0.19.1.tgz"
  "version" "0.19.1"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"

"scheduler@^0.20.2":
  "integrity" "sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ=="
  "resolved" "https://registry.npmjs.org/scheduler/-/scheduler-0.20.2.tgz"
  "version" "0.20.2"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"

"schema-utils@^3.0.0":
  "integrity" "sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"schema-utils@^4.3.0":
  "integrity" "sha512-Gf9qqc58SpCA/xdziiHz35F4GNIWYWZrEshUc/G/r5BnLph6xpKuLeoJoQuj5WfBIx/eQLf+hmVPYHaxJu7V2g=="
  "resolved" "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "@types/json-schema" "^7.0.9"
    "ajv" "^8.9.0"
    "ajv-formats" "^2.1.1"
    "ajv-keywords" "^5.1.0"

"scroll-into-view-if-needed@^2.2.20":
  "integrity" "sha512-dGCXy99wZQivjmjIqihaBQNjryrz5rueJY7eHfTdyWEiR4ttYpsajb14rn9s5d4DY4EcY6+4+U/maARBXJedkA=="
  "resolved" "https://registry.npmmirror.com/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.31.tgz"
  "version" "2.2.31"
  dependencies:
    "compute-scroll-into-view" "^1.0.20"

"semver@^5.6.0":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^6.1.1", "semver@^6.1.2", "semver@^6.3.0", "semver@^6.3.1":
  "integrity" "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA=="
  "resolved" "https://registry.npmmirror.com/semver/-/semver-6.3.1.tgz"
  "version" "6.3.1"

"semver@^7.3.4":
  "integrity" "sha512-PoeGJYh8HK4BTO/a9Tf6ZG3veo/A7ZVsYrSA6J8ny9nb3B1VrpkuN+z9OE5wfE5p6H4LchYZsegiQgbJD94ZFQ=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.3.5.tgz"
  "version" "7.3.5"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@^7.3.5":
  "integrity" "sha512-PoeGJYh8HK4BTO/a9Tf6ZG3veo/A7ZVsYrSA6J8ny9nb3B1VrpkuN+z9OE5wfE5p6H4LchYZsegiQgbJD94ZFQ=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.3.5.tgz"
  "version" "7.3.5"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@2 || 3 || 4 || 5":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@7.0.0":
  "integrity" "sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.0.0.tgz"
  "version" "7.0.0"

"serialize-javascript@^6.0.2":
  "integrity" "sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g=="
  "resolved" "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "randombytes" "^2.1.0"

"shallowequal@^1.1.0":
  "integrity" "sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ=="
  "resolved" "https://registry.npmjs.org/shallowequal/-/shallowequal-1.1.0.tgz"
  "version" "1.1.0"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"side-channel@^1.0.4":
  "integrity" "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw=="
  "resolved" "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.0"
    "get-intrinsic" "^1.0.2"
    "object-inspect" "^1.9.0"

"signal-exit@^3.0.2":
  "integrity" "sha512-sDl4qMFpijcGw22U5w63KmD3cZJfBuFlVNbVMKje2keoKML7X2UzWbc4XrmEbDwg0NXJc3yv4/ox7b+JWb57kQ=="
  "resolved" "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.6.tgz"
  "version" "3.0.6"

"simple-statistics@^6.1.0":
  "integrity" "sha512-zGwn0DDRa9Zel4H4n2pjTFIyGoAGpnpjrGIctreCxj5XWrcx9v7Xy7270FkC967WMmcvuc8ZU7m0ZG+hGN7gAA=="
  "resolved" "https://registry.npmjs.org/simple-statistics/-/simple-statistics-6.1.1.tgz"
  "version" "6.1.1"

"simple-statistics@^7.1.0":
  "integrity" "sha512-TAsZRUJ7FD/yCnm5UBgyWU7bP1gOPsw9n/dVrE8hQ+BF1zJPgDJ5X/MOnxG+HE/7nejSpJLJLdmTh7bkfsFkRw=="
  "resolved" "https://registry.npmjs.org/simple-statistics/-/simple-statistics-7.7.0.tgz"
  "version" "7.7.0"

"simple-swizzle@^0.2.2":
  "integrity" "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo= sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg=="
  "resolved" "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "is-arrayish" "^0.3.1"

"size-sensor@^1.0.1":
  "integrity" "sha512-QTy7MnuugCFXIedXRpUSk9gUnyNiaxIdxGfUjr8xxXOqIB3QvBUYP9+b51oCg2C4dnhaeNk/h57TxjbvoJrJUA=="
  "resolved" "https://registry.npmjs.org/size-sensor/-/size-sensor-1.0.1.tgz"
  "version" "1.0.1"

"skmeans@0.9.7":
  "integrity" "sha512-hNj1/oZ7ygsfmPZ7ZfN5MUBRoGg1gtpnImuJBgLO0ljQ67DtJuiQaiYdS4lUA6s0KCwnPhGivtC/WRwIZLkHyg=="
  "resolved" "https://registry.npmjs.org/skmeans/-/skmeans-0.9.7.tgz"
  "version" "0.9.7"

"slash@^3.0.0":
  "integrity" "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q=="
  "resolved" "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
  "version" "3.0.0"

"slice-ansi@^4.0.0":
  "integrity" "sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ=="
  "resolved" "https://registry.npmjs.org/slice-ansi/-/slice-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "astral-regex" "^2.0.0"
    "is-fullwidth-code-point" "^3.0.0"

"source-map-js@^1.2.1":
  "integrity" "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA=="
  "resolved" "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.2.1.tgz"
  "version" "1.2.1"

"source-map-support@^0.3.2":
  "integrity" "sha1-NJAJd9W6PwfHdX7nLnO7GptTdU8= sha512-9O4+y9n64RewmFoKUZ/5Tx9IHIcXM6Q+RTSw6ehnqybUz4a7iwR3Eaw80uLtqqQ5D0C+5H03D4KKGo9PdP33Gg=="
  "resolved" "https://registry.npmjs.org/source-map-support/-/source-map-support-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "source-map" "0.1.32"

"source-map-support@~0.5.20":
  "integrity" "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w=="
  "resolved" "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz"
  "version" "0.5.21"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map@^0.6.0":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@~0.5.1":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w= sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@~0.6.0":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@0.1.32":
  "integrity" "sha1-yLbBZ3l7pHQKjqMyUhYv8IWRsmY= sha512-htQyLrrRLkQ87Zfrir4/yN+vAUd6DNjVayEjTSHXu29AYQJw57I4/xEL/M6p6E/woPNJwvZt6rVlzc7gFEJccQ=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.1.32.tgz"
  "version" "0.1.32"
  dependencies:
    "amdefine" ">=0.0.4"

"spdx-correct@^3.0.0":
  "integrity" "sha512-cOYcUWwhCuHCXi49RhFRCyJEK3iPj1Ziz9DpViV3tbZOwXD49QzIN3MpOLJNxh2qwq2lJJZaKMVw9qNi4jTC0w=="
  "resolved" "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A=="
  "resolved" "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
  "version" "2.3.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q=="
  "resolved" "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha512-Ctl2BrFiM0X3MANYgj3CkygxhRmr9mi6xhejbdO960nF6EDJApTYpn0BQnDKlnNBULKiCN1n3w9EBkHK8ZWg+g=="
  "resolved" "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.11.tgz"
  "version" "3.0.11"

"specificity@^0.4.1":
  "integrity" "sha512-1klA3Gi5PD1Wv9Q0wUoOQN1IWAuPu0D1U03ThXTr0cJ20+/iq2tHSDnK7Kk/0LXJ1ztUB2/1Os0wKmfyNgUQfg=="
  "resolved" "https://registry.npmjs.org/specificity/-/specificity-0.4.1.tgz"
  "version" "0.4.1"

"splaytree@^3.1.0":
  "integrity" "sha512-gvUGR7xnOy0fLKTCxDeUZYgU/I1Tdf8M/lM1Qrf8L2TIOR5ipZjGk02uYcdv0o2x7WjVRgpm3iS2clLyuVAt0Q=="
  "resolved" "https://registry.npmjs.org/splaytree/-/splaytree-3.1.0.tgz"
  "version" "3.1.0"

"split-on-first@^1.0.0":
  "integrity" "sha512-43ZssAJaMusuKWL8sKUBQXHWOpq8d6CfN/u1p4gUzfJkM05C8rxTmYrkIPTXapZpORA6LkkzcUulJ8FqA7Uudw=="
  "resolved" "https://registry.npmjs.org/split-on-first/-/split-on-first-1.1.0.tgz"
  "version" "1.1.0"

"sprintf-js@~1.0.2":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw= sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g=="
  "resolved" "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"stable@^0.1.8":
  "integrity" "sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w=="
  "resolved" "https://registry.npmjs.org/stable/-/stable-0.1.8.tgz"
  "version" "0.1.8"

"strict-uri-encode@^2.0.0":
  "integrity" "sha1-ucczDHBChi9rFC3CdLvMWGbONUY= sha512-QwiXZgpRcKkhTj2Scnn++4PKtWsH0kpzZ62L2R6c/LUVYv7hVnZqcg2+sMuT6R7Jusu1vviK/MFsu6kNJfWlEQ=="
  "resolved" "https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-2.0.0.tgz"
  "version" "2.0.0"

"string-width@^4.2.3":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string.prototype.matchall@^4.0.6":
  "integrity" "sha512-6WgDX8HmQqvEd7J+G6VtAahhsQIssiZ8zl7zKh1VDMFyL3hRTJP4FTNA3RbIp2TOQ9AYNDcc7e3fH0Qbup+DBg=="
  "resolved" "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.6.tgz"
  "version" "4.0.6"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"
    "get-intrinsic" "^1.1.1"
    "has-symbols" "^1.0.2"
    "internal-slot" "^1.0.3"
    "regexp.prototype.flags" "^1.3.1"
    "side-channel" "^1.0.4"

"string.prototype.trim@~1.2.4":
  "integrity" "sha512-Lnh17webJVsD6ECeovpVN17RlAKjmz4rF9S+8Y45CkMc/ufVpTkU3vZIyIC7sllQ1FCvObZnnCdNs/HXTUOTlg=="
  "resolved" "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.5.tgz"
  "version" "1.2.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.1"

"string.prototype.trimend@^1.0.4":
  "integrity" "sha512-y9xCjw1P23Awk8EvTpcyL2NIr1j7wJ39f+k6lvRnSMz+mz9CGz9NYPelDk42kOz6+ql8xjfK8oYzy3jAP5QU5A=="
  "resolved" "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"string.prototype.trimstart@^1.0.4":
  "integrity" "sha512-jh6e984OBfvxS50tdY2nRZnoC5/mLFKOREQfw8t5yytkoUsJRNxvI/E39qu1sD0OtWI3OC0XgKSmcWwziwYuZw=="
  "resolved" "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"strip-ansi@^3.0.0":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8= sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-final-newline@^2.0.0":
  "integrity" "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA=="
  "resolved" "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-indent@^3.0.0":
  "integrity" "sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ=="
  "resolved" "https://registry.npmjs.org/strip-indent/-/strip-indent-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "min-indent" "^1.0.0"

"strip-json-comments@^3.1.0", "strip-json-comments@^3.1.1":
  "integrity" "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig=="
  "resolved" "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"style-search@^0.1.0":
  "integrity" "sha1-eVjHk+R+MuB9K1yv5cC/jhLneQI= sha512-Dj1Okke1C3uKKwQcetra4jSuk0DqbzbYtXipzFlFMZtowbF1x7BKJwB9AayVMyFARvU8EDrZdcax4At/452cAg=="
  "resolved" "https://registry.npmjs.org/style-search/-/style-search-0.1.0.tgz"
  "version" "0.1.0"

"stylelint-config-prettier@^9.0.3":
  "integrity" "sha512-U44lELgLZhbAD/xy/vncZ2Pq8sh2TnpiPvo38Ifg9+zeioR+LAkHu0i6YORIOxFafZoVg0xqQwex6e6F25S5XA=="
  "resolved" "https://registry.npmmirror.com/stylelint-config-prettier/-/stylelint-config-prettier-9.0.5.tgz"
  "version" "9.0.5"

"stylelint-config-recommended@^6.0.0":
  "integrity" "sha512-ZorSSdyMcxWpROYUvLEMm0vSZud2uB7tX1hzBZwvVY9SV/uly4AvvJPPhCcymZL3fcQhEQG5AELmrxWqtmzacw=="
  "resolved" "https://registry.npmjs.org/stylelint-config-recommended/-/stylelint-config-recommended-6.0.0.tgz"
  "version" "6.0.0"

"stylelint-config-standard@^24.0.0":
  "integrity" "sha512-+RtU7fbNT+VlNbdXJvnjc3USNPZRiRVp/d2DxOF/vBDDTi0kH5RX2Ny6errdtZJH3boO+bmqIYEllEmok4jiuw=="
  "resolved" "https://registry.npmjs.org/stylelint-config-standard/-/stylelint-config-standard-24.0.0.tgz"
  "version" "24.0.0"
  dependencies:
    "stylelint-config-recommended" "^6.0.0"

"stylelint@^14.0.0", "stylelint@^14.1.0", "stylelint@>= 11.x < 15":
  "integrity" "sha512-IedkssuNVA11+v++2PIV2OHOU5A3SfRcXVi56vZVSsMhGrgtwmmit69jeM+08/Tun5DTBe7BuH1Zp1mMLmtKLA=="
  "resolved" "https://registry.npmjs.org/stylelint/-/stylelint-14.1.0.tgz"
  "version" "14.1.0"
  dependencies:
    "balanced-match" "^2.0.0"
    "cosmiconfig" "^7.0.1"
    "debug" "^4.3.2"
    "execall" "^2.0.0"
    "fast-glob" "^3.2.7"
    "fastest-levenshtein" "^1.0.12"
    "file-entry-cache" "^6.0.1"
    "get-stdin" "^8.0.0"
    "global-modules" "^2.0.0"
    "globby" "^11.0.4"
    "globjoin" "^0.1.4"
    "html-tags" "^3.1.0"
    "ignore" "^5.1.9"
    "import-lazy" "^4.0.0"
    "imurmurhash" "^0.1.4"
    "is-plain-object" "^5.0.0"
    "known-css-properties" "^0.23.0"
    "mathml-tag-names" "^2.1.3"
    "meow" "^9.0.0"
    "micromatch" "^4.0.4"
    "normalize-path" "^3.0.0"
    "normalize-selector" "^0.2.0"
    "picocolors" "^1.0.0"
    "postcss" "^8.3.11"
    "postcss-media-query-parser" "^0.2.3"
    "postcss-resolve-nested-selector" "^0.1.1"
    "postcss-safe-parser" "^6.0.0"
    "postcss-selector-parser" "^6.0.6"
    "postcss-value-parser" "^4.1.0"
    "resolve-from" "^5.0.0"
    "specificity" "^0.4.1"
    "string-width" "^4.2.3"
    "strip-ansi" "^6.0.1"
    "style-search" "^0.1.0"
    "svg-tags" "^1.0.0"
    "table" "^6.7.3"
    "v8-compile-cache" "^2.3.0"
    "write-file-atomic" "^3.0.3"

"supports-color@^2.0.0":
  "integrity" "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc= sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz"
  "version" "2.0.0"

"supports-color@^5.3.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-color@^8.0.0":
  "integrity" "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
  "version" "8.1.1"
  dependencies:
    "has-flag" "^4.0.0"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="
  "resolved" "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"svg-parser@^2.0.2":
  "integrity" "sha512-e4hG1hRwoOdRb37cIMSgzNsxyzKfayW6VOflrwvR+/bzrkyxY/31WkbgnQpgtrNp1SdpJvpUAGTa/ZoiPNDuRQ=="
  "resolved" "https://registry.npmjs.org/svg-parser/-/svg-parser-2.0.4.tgz"
  "version" "2.0.4"

"svg-tags@^1.0.0":
  "integrity" "sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q= sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA=="
  "resolved" "https://registry.npmjs.org/svg-tags/-/svg-tags-1.0.0.tgz"
  "version" "1.0.0"

"svgo@^1.2.2":
  "integrity" "sha512-yhy/sQYxR5BkC98CY7o31VGsg014AKLEPxdfhora76l36hD9Rdy5NZA/Ocn6yayNPgSamYdtX2rFJdcv07AYVw=="
  "resolved" "https://registry.npmjs.org/svgo/-/svgo-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "chalk" "^2.4.1"
    "coa" "^2.0.2"
    "css-select" "^2.0.0"
    "css-select-base-adapter" "^0.1.1"
    "css-tree" "1.0.0-alpha.37"
    "csso" "^4.0.2"
    "js-yaml" "^3.13.1"
    "mkdirp" "~0.5.1"
    "object.values" "^1.1.0"
    "sax" "~1.2.4"
    "stable" "^0.1.8"
    "unquote" "~1.1.1"
    "util.promisify" "~1.0.0"

"table@^6.7.3":
  "integrity" "sha512-5DkIxeA7XERBqMwJq0aHZOdMadBx4e6eDoFRuyT5VR82J0Ycg2DwM6GfA/EQAhJ+toRTaS1lIdSQCqgrmhPnlw=="
  "resolved" "https://registry.npmjs.org/table/-/table-6.7.3.tgz"
  "version" "6.7.3"
  dependencies:
    "ajv" "^8.0.1"
    "lodash.truncate" "^4.4.2"
    "slice-ansi" "^4.0.0"
    "string-width" "^4.2.3"
    "strip-ansi" "^6.0.1"

"tapable@^2.1.1", "tapable@^2.2.0":
  "integrity" "sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ=="
  "resolved" "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz"
  "version" "2.2.1"

"tape@^4.5.1":
  "integrity" "sha512-z0+WrUUJuG6wIdWrl4W3rTte2CR26G6qcPOj3w1hfRdcmhF3kHBhOBW9VHsPVAkz08ZmGzp7phVpDupbLzrYKQ=="
  "resolved" "https://registry.npmjs.org/tape/-/tape-4.14.0.tgz"
  "version" "4.14.0"
  dependencies:
    "call-bind" "~1.0.2"
    "deep-equal" "~1.1.1"
    "defined" "~1.0.0"
    "dotignore" "~0.1.2"
    "for-each" "~0.3.3"
    "glob" "~7.1.7"
    "has" "~1.0.3"
    "inherits" "~2.0.4"
    "is-regex" "~1.1.3"
    "minimist" "~1.2.5"
    "object-inspect" "~1.11.0"
    "resolve" "~1.20.0"
    "resumer" "~0.0.0"
    "string.prototype.trim" "~1.2.4"
    "through" "~2.3.8"

"terser-webpack-plugin@^5.3.11":
  "integrity" "sha512-RVCsMfuD0+cTt3EwX8hSl2Ks56EbFHWmhluwcqoPKtBnfjiT6olaq7PRIRfhyU8nnC2MrnDrBLfrD/RGE+cVXQ=="
  "resolved" "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.11.tgz"
  "version" "5.3.11"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    "jest-worker" "^27.4.5"
    "schema-utils" "^4.3.0"
    "serialize-javascript" "^6.0.2"
    "terser" "^5.31.1"

"terser@^5.31.1":
  "integrity" "sha512-LBAhFyLho16harJoWMg/nZsQYgTrg5jXOn2nCYjRUcZZEdE3qa2zb8QEDRUGVZBW4rlazf2fxkg8tztybTaqWw=="
  "resolved" "https://registry.npmjs.org/terser/-/terser-5.39.0.tgz"
  "version" "5.39.0"
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    "acorn" "^8.8.2"
    "commander" "^2.20.0"
    "source-map-support" "~0.5.20"

"text-table@^0.2.0":
  "integrity" "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ= sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw=="
  "resolved" "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  "version" "0.2.0"

"through@~2.3.4", "through@~2.3.8":
  "integrity" "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU= sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg=="
  "resolved" "https://registry.npmjs.org/through/-/through-2.3.8.tgz"
  "version" "2.3.8"

"tiny-invariant@^1.0.2":
  "integrity" "sha512-1Uhn/aqw5C6RI4KejVeTg6mIS7IqxnLJ8Mv2tV5rTc0qWobay7pDUz6Wi392Cnc8ak1H0F2cjoRzb2/AW4+Fvg=="
  "resolved" "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.2.0.tgz"
  "version" "1.2.0"

"tiny-warning@^1.0.0", "tiny-warning@^1.0.3":
  "integrity" "sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA=="
  "resolved" "https://registry.npmjs.org/tiny-warning/-/tiny-warning-1.0.3.tgz"
  "version" "1.0.3"

"tinycolor2@^1.4.1":
  "integrity" "sha512-vJhccZPs965sV/L2sU4oRQVAos0pQXwsvTLkWYdqJ+a8Q5kPFzJTuOFwy7UniPli44NKQGAglksjvOcpo95aZA=="
  "resolved" "https://registry.npmjs.org/tinycolor2/-/tinycolor2-1.4.2.tgz"
  "version" "1.4.2"

"tinyqueue@^2.0.3":
  "integrity" "sha512-ppJZNDuKGgxzkHihX8v9v9G5f+18gzaTfrukGrq6ueg0lmH4nqVnA2IPG0AEH3jKEk2GRJCUhDoqpoiw3PHLBA=="
  "resolved" "https://registry.npmjs.org/tinyqueue/-/tinyqueue-2.0.3.tgz"
  "version" "2.0.3"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"toggle-selection@^1.0.6":
  "integrity" "sha1-bkWxJj8gF/oKzH2J14sVuL932jI= sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ=="
  "resolved" "https://registry.npmjs.org/toggle-selection/-/toggle-selection-1.0.6.tgz"
  "version" "1.0.6"

"topojson-client@^3.0.0", "topojson-client@3.x":
  "integrity" "sha512-605uxS6bcYxGXw9qi62XyrV6Q3xwbndjachmNxu8HWTtVPxZfEJN9fd/SZS1Q54Sn2y0TMyMxFj/cJINqGHrKw=="
  "resolved" "https://registry.npmjs.org/topojson-client/-/topojson-client-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "commander" "2"

"topojson-server@3.x":
  "integrity" "sha512-/VS9j/ffKr2XAOjlZ9CgyyeLmgJ9dMwq6Y0YEON8O7p/tGGk+dCWnrE03zEdu7i4L7YsFZLEPZPzCvcB7lEEXw=="
  "resolved" "https://registry.npmjs.org/topojson-server/-/topojson-server-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "commander" "2"

"trim-newlines@^3.0.0":
  "integrity" "sha512-c1PTsA3tYrIsLGkJkzHF+w9F2EyxfXGo4UyJc4pFL++FMjnq0HJS69T3M7d//gKrFKwy429bouPescbjecU+Zw=="
  "resolved" "https://registry.npmjs.org/trim-newlines/-/trim-newlines-3.0.1.tgz"
  "version" "3.0.1"

"tslib@^1.10.0":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^1.8.1":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^2.0.0", "tslib@^2.0.3", "tslib@^2.1.0", "tslib@^2.3.0":
  "integrity" "sha512-77EbyPPpMz+FRFRuAFlWMtmgUWGe9UOG2Z25NqCwiIjRhOf5iKGuzSe5P2w1laq+FkRy4p+PCuVkJSGkzTEKVw=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.3.1.tgz"
  "version" "2.3.1"

"tslib@^2.6.2":
  "integrity" "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="
  "resolved" "https://registry.npmmirror.com/tslib/-/tslib-2.8.1.tgz"
  "version" "2.8.1"

"tsutils@^3.21.0":
  "integrity" "sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA=="
  "resolved" "https://registry.npmjs.org/tsutils/-/tsutils-3.21.0.tgz"
  "version" "3.21.0"
  dependencies:
    "tslib" "^1.8.1"

"turf-jsts@*":
  "integrity" "sha512-Ja03QIJlPuHt4IQ2FfGex4F4JAr8m3jpaHbFbQrgwr7s7L6U8ocrHiF3J1+wf9jzhGKxvDeaCAnGDot8OjGFyA=="
  "resolved" "https://registry.npmjs.org/turf-jsts/-/turf-jsts-1.2.3.tgz"
  "version" "1.2.3"

"type-check@^0.4.0", "type-check@~0.4.0":
  "integrity" "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew=="
  "resolved" "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-fest@^0.18.0":
  "integrity" "sha512-OIAYXk8+ISY+qTOwkHtKqzAuxchoMiD9Udx+FSGQDuiRR+PJKJHc2NJAXlbhkGwTt/4/nKZxELY1w3ReWOL8mw=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.18.1.tgz"
  "version" "0.18.1"

"type-fest@^0.20.2":
  "integrity" "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
  "version" "0.20.2"

"type-fest@^0.6.0":
  "integrity" "sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.6.0.tgz"
  "version" "0.6.0"

"type-fest@^0.8.1":
  "integrity" "sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.8.1.tgz"
  "version" "0.8.1"

"typedarray-to-buffer@^3.1.5":
  "integrity" "sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q=="
  "resolved" "https://registry.npmjs.org/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz"
  "version" "3.1.5"
  dependencies:
    "is-typedarray" "^1.0.0"

"typescript@^4.5.2", "typescript@>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta":
  "integrity" "sha512-5BlMof9H1yGt0P8/WF+wPNw6GfctgGjXp5hkblpyT+8rkASSmkUKMXrxR0Xg8ThVCi/JnHQiKXeBaEwCeQwMFw=="
  "resolved" "https://registry.npmjs.org/typescript/-/typescript-4.5.2.tgz"
  "version" "4.5.2"

"uglify-js@^2.6.2":
  "integrity" "sha1-KcVzMUgFe7Th913zW3qcty5qWd0= sha512-qLq/4y2pjcU3vhlhseXGGJ7VbFO4pBANu0kwl8VCa9KEI0V8VfZIx2Fy3w01iSTA/pGwKZSmu/+I4etLNDdt5w=="
  "resolved" "https://registry.npmjs.org/uglify-js/-/uglify-js-2.8.29.tgz"
  "version" "2.8.29"
  dependencies:
    "source-map" "~0.5.1"
    "yargs" "~3.10.0"
  optionalDependencies:
    "uglify-to-browserify" "~1.0.0"

"uglify-to-browserify@~1.0.0":
  "integrity" "sha1-bgkk1r2mta/jSeOabWMoUKD4grc= sha512-vb2s1lYx2xBtUgy+ta+b2J/GLVUR+wmpINwHePmPRhOsIVCG2wDzKJ0n14GslH1BifsqVzSOwQhRaCAsZ/nI4Q=="
  "resolved" "https://registry.npmjs.org/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz"
  "version" "1.0.2"

"unbox-primitive@^1.0.1":
  "integrity" "sha512-tZU/3NqK3dA5gpE1KtyiJUrEB0lxnGkMFHptJ7q6ewdZ8s12QrODwNbhIJStmJkd1QDXa1NRA8aF2A1zk/Ypyw=="
  "resolved" "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "function-bind" "^1.1.1"
    "has-bigints" "^1.0.1"
    "has-symbols" "^1.0.2"
    "which-boxed-primitive" "^1.0.2"

"unicode-canonical-property-names-ecmascript@^2.0.0":
  "integrity" "sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ=="
  "resolved" "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unicode-match-property-ecmascript@^2.0.0":
  "integrity" "sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q=="
  "resolved" "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^2.0.0"
    "unicode-property-aliases-ecmascript" "^2.0.0"

"unicode-match-property-value-ecmascript@^2.0.0":
  "integrity" "sha512-7Yhkc0Ye+t4PNYzOGKedDhXbYIBe1XEQYQxOPyhcXNMJ0WCABqqj6ckydd6pWRZTHV4GuCPKdBAUiMc60tsKVw=="
  "resolved" "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unicode-property-aliases-ecmascript@^2.0.0":
  "integrity" "sha512-5Zfuy9q/DFr4tfO7ZPeVXb1aPoeQSdeFMLpYuFebehDAhbuevLs5yxSZmIFN1tP5F9Wl4IpJrYojg85/zgyZHQ=="
  "resolved" "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"universalify@^2.0.0":
  "integrity" "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw=="
  "resolved" "https://registry.npmmirror.com/universalify/-/universalify-2.0.1.tgz"
  "version" "2.0.1"

"unquote@~1.1.1":
  "integrity" "sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ= sha512-vRCqFv6UhXpWxZPyGDh/F3ZpNv8/qo7w6iufLpQg9aKnQ71qM4B5KiI7Mia9COcjEhrO9LueHpMYjYzsWH3OIg=="
  "resolved" "https://registry.npmjs.org/unquote/-/unquote-1.1.1.tgz"
  "version" "1.1.1"

"update-browserslist-db@^1.1.1":
  "integrity" "sha512-PPypAm5qvlD7XMZC3BujecnaOxwhrtoFR+Dqkk5Aa/6DssiH0ibKoketaj9w8LP7Bont1rYeoV5plxD7RTEPRg=="
  "resolved" "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "escalade" "^3.2.0"
    "picocolors" "^1.1.1"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"use-callback-ref@^1.3.2":
  "integrity" "sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg=="
  "resolved" "https://registry.npmmirror.com/use-callback-ref/-/use-callback-ref-1.3.3.tgz"
  "version" "1.3.3"
  dependencies:
    "tslib" "^2.0.0"

"use-sidecar@^1.1.2":
  "integrity" "sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ=="
  "resolved" "https://registry.npmmirror.com/use-sidecar/-/use-sidecar-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "detect-node-es" "^1.1.0"
    "tslib" "^2.0.0"

"util-deprecate@^1.0.2":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8= sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="
  "resolved" "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util.promisify@~1.0.0":
  "integrity" "sha512-g9JpC/3He3bm38zsLupWryXHoEcS22YHthuPQSJdMy6KNrzIRzWqcsHzD/WUnqe45whVou4VIsPew37DoXWNrA=="
  "resolved" "https://registry.npmjs.org/util.promisify/-/util.promisify-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "define-properties" "^1.1.3"
    "es-abstract" "^1.17.2"
    "has-symbols" "^1.0.1"
    "object.getownpropertydescriptors" "^2.1.0"

"v8-compile-cache@^2.0.3", "v8-compile-cache@^2.3.0":
  "integrity" "sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA=="
  "resolved" "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz"
  "version" "2.3.0"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew=="
  "resolved" "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"value-equal@^1.0.1":
  "integrity" "sha512-NOJ6JZCAWr0zlxZt+xqCHNTEKOsrks2HQd4MqhP1qy4z1SkbEP467eNx6TgDKXMvUOb+OENfJCZwM+16n7fRfw=="
  "resolved" "https://registry.npmjs.org/value-equal/-/value-equal-1.0.1.tgz"
  "version" "1.0.1"

"vite@^2.6.0", "vite@^2.9.18":
  "integrity" "sha512-sAOqI5wNM9QvSEE70W3UGMdT8cyEn0+PmJMTFvTB8wB0YbYUWw3gUbY62AOyrXosGieF2htmeLATvNxpv/zNyQ=="
  "resolved" "https://registry.npmmirror.com/vite/-/vite-2.9.18.tgz"
  "version" "2.9.18"
  dependencies:
    "esbuild" "^0.14.27"
    "postcss" "^8.4.13"
    "resolve" "^1.22.0"
    "rollup" ">=2.59.0 <2.78.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"warning@^4.0.3":
  "integrity" "sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w=="
  "resolved" "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "loose-envify" "^1.0.0"

"watchpack@^2.4.1":
  "integrity" "sha512-TnbFSbcOCcDgjZ4piURLCbJ3nJhznVh9kw6F6iokjiFPl8ONxe9A6nMDVXDiNbrSfLILs6vB07F7wLBrwPYzJw=="
  "resolved" "https://registry.npmjs.org/watchpack/-/watchpack-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.1.2"

"webpack-sources@^3.2.3":
  "integrity" "sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w=="
  "resolved" "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz"
  "version" "3.2.3"

"webpack@^4.0.0 || ^5.0.0", "webpack@^5.1.0":
  "integrity" "sha512-UFynvx+gM44Gv9qFgj0acCQK2VE1CtdfwFdimkapco3hlPCJ/zeq73n2yVKimVbtm+TnApIugGhLJnkU6gjYXA=="
  "resolved" "https://registry.npmjs.org/webpack/-/webpack-5.98.0.tgz"
  "version" "5.98.0"
  dependencies:
    "@types/eslint-scope" "^3.7.7"
    "@types/estree" "^1.0.6"
    "@webassemblyjs/ast" "^1.14.1"
    "@webassemblyjs/wasm-edit" "^1.14.1"
    "@webassemblyjs/wasm-parser" "^1.14.1"
    "acorn" "^8.14.0"
    "browserslist" "^4.24.0"
    "chrome-trace-event" "^1.0.2"
    "enhanced-resolve" "^5.17.1"
    "es-module-lexer" "^1.2.1"
    "eslint-scope" "5.1.1"
    "events" "^3.2.0"
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.2.11"
    "json-parse-even-better-errors" "^2.3.1"
    "loader-runner" "^4.2.0"
    "mime-types" "^2.1.27"
    "neo-async" "^2.6.2"
    "schema-utils" "^4.3.0"
    "tapable" "^2.1.1"
    "terser-webpack-plugin" "^5.3.11"
    "watchpack" "^2.4.1"
    "webpack-sources" "^3.2.3"

"which-boxed-primitive@^1.0.2":
  "integrity" "sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg=="
  "resolved" "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-bigint" "^1.0.1"
    "is-boolean-object" "^1.1.0"
    "is-number-object" "^1.0.4"
    "is-string" "^1.0.5"
    "is-symbol" "^1.0.3"

"which@^1.3.1":
  "integrity" "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ=="
  "resolved" "https://registry.npmjs.org/which/-/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"window-size@0.1.0":
  "integrity" "sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0= sha512-1pTPQDKTdd61ozlKGNCjhNRd+KPmgLSGa3mZTHoOliaGcESD8G1PXhh7c1fgiPjVbNVfgy2Faw4BI8/m0cC8Mg=="
  "resolved" "https://registry.npmjs.org/window-size/-/window-size-0.1.0.tgz"
  "version" "0.1.0"

"wolfy87-eventemitter@^5.1.0":
  "integrity" "sha512-P+6vtWyuDw+MB01X7UeF8TaHBvbCovf4HPEMF/SV7BdDc1SMTiBy13SRD71lQh4ExFTG1d/WNzDGDCyOKSMblw=="
  "resolved" "https://registry.npmjs.org/wolfy87-eventemitter/-/wolfy87-eventemitter-5.2.9.tgz"
  "version" "5.2.9"

"word-wrap@^1.2.3":
  "integrity" "sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ=="
  "resolved" "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"wordwrap@0.0.2":
  "integrity" "sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8= sha512-xSBsCeh+g+dinoBv3GAOWM4LcVVO68wLXRanibtBSdUvkGWQRGeE9P7IwU9EmDDi4jA6L44lz15CGMwdw9N5+Q=="
  "resolved" "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz"
  "version" "0.0.2"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8= sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="
  "resolved" "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write-file-atomic@^3.0.3":
  "integrity" "sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q=="
  "resolved" "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "imurmurhash" "^0.1.4"
    "is-typedarray" "^1.0.0"
    "signal-exit" "^3.0.2"
    "typedarray-to-buffer" "^3.1.5"

"yallist@^3.0.2":
  "integrity" "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g=="
  "resolved" "https://registry.npmmirror.com/yallist/-/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yallist@^4.0.0":
  "integrity" "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yaml@^1.10.0":
  "integrity" "sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg=="
  "resolved" "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz"
  "version" "1.10.2"

"yargs-parser@^20.2.3":
  "integrity" "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz"
  "version" "20.2.9"

"yargs@~3.10.0":
  "integrity" "sha1-9+572FfdfB0tOMDnTvvWgdFDH9E= sha512-QFzUah88GAGy9lyDKGBqZdkYApt63rCXYBGYnEP4xDJPXNqXXnBDACnbrXnViV6jRSqAePwrATi2i8mfYm4L1A=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-3.10.0.tgz"
  "version" "3.10.0"
  dependencies:
    "camelcase" "^1.0.2"
    "cliui" "^2.1.0"
    "decamelize" "^1.0.0"
    "window-size" "0.1.0"
