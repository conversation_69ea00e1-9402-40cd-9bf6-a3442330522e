# 探索页管理功能

## 功能概述

根据原型图设计，实现了一个完整的模块内容管理系统，包含以下主要功能：

### 1. 页面结构

- **页面头部**：包含面包屑导航、页面/模块切换器、保存/确存按钮
- **模块属性**：显示模块ID、模块名称、模版信息
- **策略设置**：左侧区域，包含策略配置表单
- **内容管理**：右侧区域，包含内容编辑表单
- **策略管理表格**：底部表格，显示所有策略列表

### 2. 主要功能

#### 模块属性
- 显示当前模块的基本信息
- 模块ID：11
- 模块名称：金融区导航
- 模版：横向卡片

#### 策略设置
- **策略名称**：可输入策略名称
- **策略优先级**：下拉选择1-3优先级
- **受众条件**：复选框选择（全部、车主、成年人、未成年）
- **时间设置**：上线时间和下线时间选择器
- **样式设置**：样式预览区域

#### 内容管理
- **卡片标题**：输入内容标题
- **营销设置**：
  - 文字输入
  - Toast提示文案
  - 社会化证明文案
  - 自动完成提醒文案
- **内容设置**：
  - 图片上传（封面图、半屏图、全屏图竖版、全屏图横版）
  - 图片管理（上传、删除、替换、下移）
  - 添加新项功能

#### 策略管理表格
- **表格列**：策略ID、策略名称、状态、上下线时间、创建人、优先级、受众条件、操作
- **操作功能**：申请上线、复制、删除
- **批量操作**：批量提交审核、批量删除
- **筛选功能**：按状态和群体筛选

### 3. 技术实现

#### 组件结构
```
admin-dashboard/src/pages/app-website/explore-management/
├── index.tsx           # 主页面组件
├── style/
│   └── index.module.less  # 样式文件
├── locale/
│   └── index.ts        # 国际化文件
└── README.md          # 功能说明文档
```

#### 主要技术栈
- React + TypeScript
- Arco Design 组件库
- CSS Modules
- 模拟数据（可替换为真实API）

#### 状态管理
- 模块属性状态
- 策略数据状态
- 内容数据状态
- 表单状态管理

### 4. 使用方法

1. 启动开发服务器：`npm run dev`
2. 访问：http://localhost:3000
3. 登录后台管理系统
4. 导航到：APP官网 → 探索页管理

### 5. 功能特性

- ✅ 响应式布局设计
- ✅ 表单验证和数据处理
- ✅ 图片上传和管理
- ✅ 表格操作和筛选
- ✅ 状态实时更新
- ✅ 批量操作支持
- ✅ 国际化支持

### 6. 后续扩展

- [ ] 接入真实API
- [ ] 添加权限控制
- [ ] 实现图片上传功能
- [ ] 添加表单验证规则
- [ ] 优化样式和交互
- [ ] 添加更多操作功能

### 7. 注意事项

- 当前使用模拟数据，需要替换为真实API
- 图片上传功能需要配置文件服务器
- 部分操作仅显示消息提示，需要实现具体业务逻辑
