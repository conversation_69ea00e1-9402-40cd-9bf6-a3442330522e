import React, { useState, useEffect, useMemo } from 'react';
import {
  Table,
  Card,
  PaginationProps,
  Button,
  Space,
  Typography,
  Message,
  Modal,
  Popconfirm,
} from '@arco-design/web-react';
import {
  IconEdit,
  IconEye,
  IconCopy,
  IconCheck,
  IconClose,
  IconClockCircle,
} from '@arco-design/web-react/icon';
import axios from 'axios';
import useLocale from '@/utils/useLocale';
import SearchForm from './form';
import locale from './locale';
import styles from './style/index.module.less';
import { getColumns } from './constants';

const { Title } = Typography;

export interface ExploreItem {
  id: string;
  title: string;
  contentType: number; // 0: 图文, 1: 横版短视频, 2: 竖版短视频
  status: number; // 0: 未上线, 1: 已上线, 2: 审核中
  createTime: string;
  updateTime: string;
  author: string;
  viewCount: number;
  likeCount: number;
}

function ExploreManagement() {
  const t = useLocale(locale);

  const [data, setData] = useState<ExploreItem[]>([]);
  const [pagination, setPagination] = useState<PaginationProps>({
    sizeCanChange: true,
    showTotal: true,
    pageSize: 10,
    current: 1,
    pageSizeChangeResetCurrent: true,
  });
  const [loading, setLoading] = useState(true);
  const [formParams, setFormParams] = useState({});

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize, JSON.stringify(formParams)]);

  function fetchData() {
    const { current, pageSize } = pagination;
    setLoading(true);
    
    // 模拟API调用
    setTimeout(() => {
      const mockData: ExploreItem[] = [
        {
          id: '1',
          title: '小米汽车SU7深度评测',
          contentType: 0,
          status: 1,
          createTime: '2024-01-15 10:30:00',
          updateTime: '2024-01-15 14:20:00',
          author: '张三',
          viewCount: 12580,
          likeCount: 856,
        },
        {
          id: '2',
          title: '智能驾驶体验分享',
          contentType: 1,
          status: 0,
          createTime: '2024-01-14 16:45:00',
          updateTime: '2024-01-14 18:30:00',
          author: '李四',
          viewCount: 8920,
          likeCount: 432,
        },
        {
          id: '3',
          title: '充电桩使用指南',
          contentType: 2,
          status: 2,
          createTime: '2024-01-13 09:15:00',
          updateTime: '2024-01-13 11:45:00',
          author: '王五',
          viewCount: 5640,
          likeCount: 298,
        },
      ];

      setData(mockData);
      setPagination({
        ...pagination,
        current,
        pageSize,
        total: mockData.length,
      });
      setLoading(false);
    }, 1000);
  }

  function onChangeTable({ current, pageSize }) {
    setPagination({
      ...pagination,
      current,
      pageSize,
    });
  }

  function handleSearch(params) {
    setPagination({ ...pagination, current: 1 });
    setFormParams(params);
  }

  // 操作回调函数
  const handleOperation = async (record: ExploreItem, type: string) => {
    console.log('操作:', type, '记录:', record);
    
    switch (type) {
      case 'edit':
        Message.info(`编辑内容: ${record.title}`);
        break;
      case 'view':
        Message.info(`查看内容: ${record.title}`);
        break;
      case 'copy':
        Message.success(`复制成功: ${record.title}`);
        break;
      case 'online':
        Message.success(`上线成功: ${record.title}`);
        // 更新状态
        setData(data.map(item => 
          item.id === record.id ? { ...item, status: 1 } : item
        ));
        break;
      case 'offline':
        Message.success(`下线成功: ${record.title}`);
        // 更新状态
        setData(data.map(item => 
          item.id === record.id ? { ...item, status: 0 } : item
        ));
        break;
      case 'submit':
        Message.success(`提交审核成功: ${record.title}`);
        // 更新状态
        setData(data.map(item => 
          item.id === record.id ? { ...item, status: 2 } : item
        ));
        break;
      default:
        break;
    }
  };

  const columns = useMemo(() => getColumns(t, handleOperation), [t]);

  return (
    <Card>
      <Title heading={6}>探索页管理</Title>
      <SearchForm onSearch={handleSearch} />
      <div className={styles['button-group']}>
        <Space>
          <Button type="primary">
            新增内容
          </Button>
          <Button>
            批量导入
          </Button>
        </Space>
        <Space>
          <Button>
            导出数据
          </Button>
        </Space>
      </div>
      <Table
        rowKey="id"
        loading={loading}
        onChange={onChangeTable}
        pagination={pagination}
        columns={columns}
        data={data}
        scroll={{ x: 1200 }}
      />
    </Card>
  );
}

export default ExploreManagement;
