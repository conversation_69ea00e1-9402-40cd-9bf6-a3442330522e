import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Space, Popconfirm } from '@arco-design/web-react';
import {
  IconEdit,
  IconEye,
  IconCopy,
  IconCheck,
  IconClose,
  IconClockCircle,
} from '@arco-design/web-react/icon';
import dayjs from 'dayjs';
import styles from './style/index.module.less';
import { ExploreItem } from './index';

const { Text } = Typography;

export const ContentType = ['图文', '横版短视频', '竖版短视频'];
export const Status = ['未上线', '已上线', '审核中'];

const StatusIcons = [
  <IconClose key={0} style={{ color: '#f53f3f' }} />,
  <IconCheck key={1} style={{ color: '#00b42a' }} />,
  <IconClockCircle key={2} style={{ color: '#ff7d00' }} />,
];

export function getColumns(
  t: any,
  callback: (record: ExploreItem, type: string) => Promise<void>
) {
  return [
    {
      title: '内容ID',
      dataIndex: 'id',
      width: 100,
      render: (value) => <Text copyable>{value}</Text>,
    },
    {
      title: '标题',
      dataIndex: 'title',
      width: 200,
      ellipsis: true,
    },
    {
      title: '内容类型',
      dataIndex: 'contentType',
      width: 120,
      render: (value) => ContentType[value],
    },
    {
      title: '作者',
      dataIndex: 'author',
      width: 100,
    },
    {
      title: '浏览量',
      dataIndex: 'viewCount',
      width: 100,
      sorter: (a, b) => a.viewCount - b.viewCount,
      render: (value) => Number(value).toLocaleString(),
    },
    {
      title: '点赞数',
      dataIndex: 'likeCount',
      width: 100,
      sorter: (a, b) => a.likeCount - b.likeCount,
      render: (value) => Number(value).toLocaleString(),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
      render: (value) => dayjs(value).format('YYYY-MM-DD HH:mm'),
      sorter: (a, b) => dayjs(a.createTime).unix() - dayjs(b.createTime).unix(),
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 150,
      render: (value) => dayjs(value).format('YYYY-MM-DD HH:mm'),
      sorter: (a, b) => dayjs(a.updateTime).unix() - dayjs(b.updateTime).unix(),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (value) => {
        const statusConfig = [
          { status: 'error', text: Status[0] },
          { status: 'success', text: Status[1] },
          { status: 'warning', text: Status[2] },
        ];
        return <Badge status={statusConfig[value]?.status as any} text={statusConfig[value]?.text} />;
      },
    },
    {
      title: '操作',
      dataIndex: 'operations',
      width: 280,
      fixed: 'right' as const,
      render: (_, record: ExploreItem) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<IconEye />}
            onClick={() => callback(record, 'view')}
          >
            查看
          </Button>
          <Button
            type="text"
            size="small"
            icon={<IconEdit />}
            onClick={() => callback(record, 'edit')}
          >
            编辑
          </Button>
          <Button
            type="text"
            size="small"
            icon={<IconCopy />}
            onClick={() => callback(record, 'copy')}
          >
            复制
          </Button>
          {record.status === 0 && (
            <Popconfirm
              title="确定要上线这个内容吗？"
              onOk={() => callback(record, 'online')}
            >
              <Button
                type="text"
                size="small"
                status="success"
              >
                上线
              </Button>
            </Popconfirm>
          )}
          {record.status === 1 && (
            <Popconfirm
              title="确定要下线这个内容吗？"
              onOk={() => callback(record, 'offline')}
            >
              <Button
                type="text"
                size="small"
                status="danger"
              >
                下线
              </Button>
            </Popconfirm>
          )}
          {(record.status === 0 || record.status === 1) && (
            <Popconfirm
              title="确定要提交审核吗？"
              onOk={() => callback(record, 'submit')}
            >
              <Button
                type="text"
                size="small"
                status="warning"
              >
                提交审核
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];
}
