const i18n = {
  'zh-CN': {
    'exploreManagement.title': '探索页管理',
    'exploreManagement.search.id': '内容ID',
    'exploreManagement.search.title': '标题',
    'exploreManagement.search.contentType': '内容类型',
    'exploreManagement.search.status': '状态',
    'exploreManagement.search.author': '作者',
    'exploreManagement.search.createTime': '创建时间',
    'exploreManagement.search.placeholder.id': '请输入内容ID',
    'exploreManagement.search.placeholder.title': '请输入内容标题',
    'exploreManagement.search.placeholder.author': '请输入作者姓名',
    'exploreManagement.search.placeholder.all': '全部',
    'exploreManagement.form.search': '搜索',
    'exploreManagement.form.reset': '重置',
    'exploreManagement.columns.id': '内容ID',
    'exploreManagement.columns.title': '标题',
    'exploreManagement.columns.contentType': '内容类型',
    'exploreManagement.columns.author': '作者',
    'exploreManagement.columns.viewCount': '浏览量',
    'exploreManagement.columns.likeCount': '点赞数',
    'exploreManagement.columns.createTime': '创建时间',
    'exploreManagement.columns.updateTime': '更新时间',
    'exploreManagement.columns.status': '状态',
    'exploreManagement.columns.operations': '操作',
    'exploreManagement.operations.view': '查看',
    'exploreManagement.operations.edit': '编辑',
    'exploreManagement.operations.copy': '复制',
    'exploreManagement.operations.online': '上线',
    'exploreManagement.operations.offline': '下线',
    'exploreManagement.operations.submit': '提交审核',
    'exploreManagement.operations.add': '新增内容',
    'exploreManagement.operations.import': '批量导入',
    'exploreManagement.operations.export': '导出数据',
  },
  'en-US': {
    'exploreManagement.title': 'Explore Management',
    'exploreManagement.search.id': 'Content ID',
    'exploreManagement.search.title': 'Title',
    'exploreManagement.search.contentType': 'Content Type',
    'exploreManagement.search.status': 'Status',
    'exploreManagement.search.author': 'Author',
    'exploreManagement.search.createTime': 'Create Time',
    'exploreManagement.search.placeholder.id': 'Please enter content ID',
    'exploreManagement.search.placeholder.title': 'Please enter title',
    'exploreManagement.search.placeholder.author': 'Please enter author name',
    'exploreManagement.search.placeholder.all': 'All',
    'exploreManagement.form.search': 'Search',
    'exploreManagement.form.reset': 'Reset',
    'exploreManagement.columns.id': 'Content ID',
    'exploreManagement.columns.title': 'Title',
    'exploreManagement.columns.contentType': 'Content Type',
    'exploreManagement.columns.author': 'Author',
    'exploreManagement.columns.viewCount': 'View Count',
    'exploreManagement.columns.likeCount': 'Like Count',
    'exploreManagement.columns.createTime': 'Create Time',
    'exploreManagement.columns.updateTime': 'Update Time',
    'exploreManagement.columns.status': 'Status',
    'exploreManagement.columns.operations': 'Operations',
    'exploreManagement.operations.view': 'View',
    'exploreManagement.operations.edit': 'Edit',
    'exploreManagement.operations.copy': 'Copy',
    'exploreManagement.operations.online': 'Online',
    'exploreManagement.operations.offline': 'Offline',
    'exploreManagement.operations.submit': 'Submit Review',
    'exploreManagement.operations.add': 'Add Content',
    'exploreManagement.operations.import': 'Batch Import',
    'exploreManagement.operations.export': 'Export Data',
  },
};

export default i18n;
