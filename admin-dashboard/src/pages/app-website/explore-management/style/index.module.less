.search-form-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background-color: var(--color-fill-2);
  border-radius: 4px;

  .search-form {
    flex: 1;
    margin-right: 20px;
  }

  .right-button {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 120px;

    button {
      width: 100%;
    }
  }
}

.button-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.content-type {
  display: flex;
  align-items: center;
  gap: 8px;

  svg {
    width: 16px;
    height: 16px;
  }
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 4px;
}

.operations {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.no-data {
  text-align: center;
  color: var(--color-text-3);
  padding: 40px 0;
}
