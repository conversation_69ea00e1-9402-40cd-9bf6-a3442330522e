import React, { useState, useEffect, useMemo } from 'react';
import {
  Table,
  Card,
  PaginationProps,
  Button,
  Space,
  Typography,
  Message,
  Form,
  Input,
  Select,
  Badge,
  Popconfirm,
} from '@arco-design/web-react';
import {
  IconEdit,
  IconEye,
  IconCopy,
  IconCheck,
  IconClose,
  IconClockCircle,
  IconPlus,
} from '@arco-design/web-react/icon';
import { useHistory } from 'react-router-dom';
import axios from 'axios';
import useLocale from '@/utils/useLocale';
import locale from './locale';
import styles from './style/index.module.less';

const { Title } = Typography;

export interface ExploreItem {
  id: string;
  title: string;
  contentType: number; // 0: 图文, 1: 横版短视频, 2: 竖版短视频
  status: number; // 0: 未上线, 1: 已上线, 2: 审核中
  createTime: string;
  updateTime: string;
  author: string;
  viewCount: number;
  likeCount: number;
}

const ContentType = ['图文', '横版短视频', '竖版短视频'];
const Status = ['未上线', '已上线', '审核中'];

function ExploreList() {
  const t = useLocale(locale);
  const history = useHistory();
  const [form] = Form.useForm();

  const [data, setData] = useState<ExploreItem[]>([]);
  const [pagination, setPagination] = useState<PaginationProps>({
    sizeCanChange: true,
    showTotal: true,
    pageSize: 10,
    current: 1,
    pageSizeChangeResetCurrent: true,
  });
  const [loading, setLoading] = useState(true);
  const [formParams, setFormParams] = useState({});

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize, JSON.stringify(formParams)]);

  function fetchData() {
    const { current, pageSize } = pagination;
    setLoading(true);

    // 模拟API调用
    setTimeout(() => {
      const mockData: ExploreItem[] = [
        {
          id: '1',
          title: '小米汽车SU7深度评测',
          contentType: 0,
          status: 1,
          createTime: '2024-01-15 10:30:00',
          updateTime: '2024-01-15 14:20:00',
          author: '张三',
          viewCount: 12580,
          likeCount: 856,
        },
        {
          id: '2',
          title: '智能驾驶体验分享',
          contentType: 1,
          status: 0,
          createTime: '2024-01-14 16:45:00',
          updateTime: '2024-01-14 18:30:00',
          author: '李四',
          viewCount: 8920,
          likeCount: 432,
        },
        {
          id: '3',
          title: '充电桩使用指南',
          contentType: 2,
          status: 2,
          createTime: '2024-01-13 09:15:00',
          updateTime: '2024-01-13 11:45:00',
          author: '王五',
          viewCount: 5640,
          likeCount: 298,
        },
      ];

      setData(mockData);
      setPagination({
        ...pagination,
        current,
        pageSize,
        total: mockData.length,
      });
      setLoading(false);
    }, 1000);
  }

  function onChangeTable({ current, pageSize }) {
    setPagination({
      ...pagination,
      current,
      pageSize,
    });
  }

  function handleSearch() {
    const values = form.getFieldsValue();
    setPagination({ ...pagination, current: 1 });
    setFormParams(values);
  }

  function handleReset() {
    form.resetFields();
    setPagination({ ...pagination, current: 1 });
    setFormParams({});
  }

  // 操作回调函数
  const handleOperation = async (record: ExploreItem, type: string) => {
    console.log('操作:', type, '记录:', record);

    switch (type) {
      case 'edit':
        // 跳转到编辑页面
        history.push(`/carsite/explore/edit?id=${record.id}`);
        break;
      case 'view':
        Message.info(`查看内容: ${record.title}`);
        break;
      case 'copy':
        Message.success(`复制成功: ${record.title}`);
        break;
      case 'online':
        Message.success(`上线成功: ${record.title}`);
        setData(data.map(item =>
          item.id === record.id ? { ...item, status: 1 } : item
        ));
        break;
      case 'offline':
        Message.success(`下线成功: ${record.title}`);
        setData(data.map(item =>
          item.id === record.id ? { ...item, status: 0 } : item
        ));
        break;
      case 'submit':
        Message.success(`提交审核成功: ${record.title}`);
        setData(data.map(item =>
          item.id === record.id ? { ...item, status: 2 } : item
        ));
        break;
      default:
        break;
    }
  };

  // 新增内容
  const handleAdd = () => {
    history.push('/carsite/explore/edit?id=new');
  };

  const columns = [
    {
      title: '内容ID',
      dataIndex: 'id',
      width: 100,
    },
    {
      title: '标题',
      dataIndex: 'title',
      width: 200,
      ellipsis: true,
    },
    {
      title: '内容类型',
      dataIndex: 'contentType',
      width: 120,
      render: (value) => ContentType[value],
    },
    {
      title: '作者',
      dataIndex: 'author',
      width: 100,
    },
    {
      title: '浏览量',
      dataIndex: 'viewCount',
      width: 100,
      sorter: (a, b) => a.viewCount - b.viewCount,
      render: (value) => Number(value).toLocaleString(),
    },
    {
      title: '点赞数',
      dataIndex: 'likeCount',
      width: 100,
      sorter: (a, b) => a.likeCount - b.likeCount,
      render: (value) => Number(value).toLocaleString(),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (value) => {
        const statusConfig = [
          { status: 'error', text: Status[0] },
          { status: 'success', text: Status[1] },
          { status: 'warning', text: Status[2] },
        ];
        return <Badge status={statusConfig[value]?.status as any} text={statusConfig[value]?.text} />;
      },
    },
    {
      title: '操作',
      dataIndex: 'operations',
      width: 280,
      fixed: 'right' as const,
      render: (_, record: ExploreItem) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<IconEye />}
            onClick={() => handleOperation(record, 'view')}
          >
            查看
          </Button>
          <Button
            type="text"
            size="small"
            icon={<IconEdit />}
            onClick={() => handleOperation(record, 'edit')}
          >
            编辑
          </Button>
          <Button
            type="text"
            size="small"
            icon={<IconCopy />}
            onClick={() => handleOperation(record, 'copy')}
          >
            复制
          </Button>
          {record.status === 0 && (
            <Popconfirm
              title="确定要上线这个内容吗？"
              onOk={() => handleOperation(record, 'online')}
            >
              <Button type="text" size="small" status="success">
                上线
              </Button>
            </Popconfirm>
          )}
          {record.status === 1 && (
            <Popconfirm
              title="确定要下线这个内容吗？"
              onOk={() => handleOperation(record, 'offline')}
            >
              <Button type="text" size="small" status="danger">
                下线
              </Button>
            </Popconfirm>
          )}
          {(record.status === 0 || record.status === 1) && (
            <Popconfirm
              title="确定要提交审核吗？"
              onOk={() => handleOperation(record, 'submit')}
            >
              <Button type="text" size="small" status="warning">
                提交审核
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <Card>
      {/* 页面标识 */}
      <div style={{
        background: '#52c41a',
        color: 'white',
        padding: '10px',
        textAlign: 'center',
        marginBottom: '16px',
        fontSize: '18px',
        fontWeight: 'bold'
      }}>
        📋 探索页管理列表页面
      </div>

      <Title heading={6}>探索页管理</Title>

      {/* 搜索表单 */}
      <div className={styles['search-form-wrapper']}>
        <Form
          form={form}
          className={styles['search-form']}
          labelAlign="left"
          labelCol={{ span: 5 }}
          wrapperCol={{ span: 19 }}
          layout="inline"
        >
          <Form.Item label="内容ID" field="id">
            <Input placeholder="请输入内容ID" allowClear style={{ width: 150 }} />
          </Form.Item>
          <Form.Item label="标题" field="title">
            <Input placeholder="请输入内容标题" allowClear style={{ width: 200 }} />
          </Form.Item>
          <Form.Item label="内容类型" field="contentType">
            <Select placeholder="请选择内容类型" allowClear style={{ width: 150 }}>
              {ContentType.map((item, index) => (
                <Select.Option key={index} value={index}>{item}</Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item label="状态" field="status">
            <Select placeholder="请选择状态" allowClear style={{ width: 120 }}>
              {Status.map((item, index) => (
                <Select.Option key={index} value={index}>{item}</Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </div>

      {/* 操作按钮 */}
      <div className={styles['button-group']}>
        <Space>
          <Button type="primary" icon={<IconPlus />} onClick={handleAdd}>
            新增内容
          </Button>
          <Button>
            批量导入
          </Button>
        </Space>
        <Space>
          <Button>
            导出数据
          </Button>
        </Space>
      </div>

      {/* 数据表格 */}
      <Table
        rowKey="id"
        loading={loading}
        onChange={onChangeTable}
        pagination={pagination}
        columns={columns}
        data={data}
        scroll={{ x: 1200 }}
      />
    </Card>
  );
}

export default ExploreList;
