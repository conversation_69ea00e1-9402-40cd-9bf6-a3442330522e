# 探索页管理功能

## 功能概述

根据原型图设计，实现了探索页管理的列表页面和编辑页面分离的功能。

## 页面结构

### 1. 列表页面 (`/carsite/explore`)
- **功能**：展示所有探索页内容的列表
- **路径**：`admin-dashboard/src/pages/carsite/explore/index.tsx`
- **主要功能**：
  - 搜索筛选（内容ID、标题、内容类型、状态）
  - 数据表格展示
  - 操作按钮（查看、编辑、复制、上线、下线、提交审核）
  - 新增内容按钮
  - 批量操作

### 2. 编辑页面 (`/carsite/explore/edit`)
- **功能**：编辑具体的探索页内容
- **路径**：`admin-dashboard/src/pages/carsite/explore/edit/index.tsx`
- **主要功能**：
  - 模块属性展示
  - 策略设置表单
  - 内容管理表单
  - 策略管理表格
  - 保存和确认保存功能
  - 返回列表功能

## 路由配置

### 路由结构
```
APP官网 (carsite)
├── 探索页管理 (carsite/explore) - 列表页面
└── 探索页编辑 (carsite/explore/edit) - 编辑页面 (隐藏菜单)
```

### URL跳转方式
- **新增内容**：`/carsite/explore/edit?id=new`
- **编辑内容**：`/carsite/explore/edit?id={contentId}`
- **返回列表**：`/carsite/explore`

## 技术实现

### 目录结构
```
admin-dashboard/src/pages/carsite/explore/
├── index.tsx                    # 列表页面
├── edit/
│   ├── index.tsx               # 编辑页面
│   ├── style/
│   │   └── index.module.less   # 编辑页面样式
│   └── locale/
│       └── index.ts            # 编辑页面国际化
├── style/
│   └── index.module.less       # 列表页面样式
├── locale/
│   └── index.ts                # 列表页面国际化
└── README.md                   # 功能说明文档
```

### 主要特性
- ✅ 列表页面与编辑页面分离
- ✅ 支持URL参数传递ID
- ✅ 新增和编辑模式自动识别
- ✅ 完整的CRUD操作
- ✅ 响应式布局设计
- ✅ 状态管理和实时更新
- ✅ 返回列表功能
- ✅ 面包屑导航

### 页面跳转逻辑
1. **列表页面 → 编辑页面**：
   - 点击"编辑"按钮：`history.push('/carsite/explore/edit?id=' + record.id)`
   - 点击"新增内容"：`history.push('/carsite/explore/edit?id=new')`

2. **编辑页面 → 列表页面**：
   - 点击"返回列表"：`history.push('/carsite/explore')`

3. **URL参数解析**：
   - 使用 `qs.parseUrl(location.search)` 解析URL参数
   - 根据 `id` 参数判断是新增还是编辑模式

## 使用方法

1. 启动开发服务器：`npm run dev`
2. 访问：http://localhost:3000
3. 登录后台管理系统
4. 导航到：APP官网 → 探索页管理

### 操作流程
1. **查看列表**：在探索页管理页面查看所有内容
2. **搜索筛选**：使用搜索表单筛选内容
3. **新增内容**：点击"新增内容"按钮跳转到编辑页面
4. **编辑内容**：点击列表中的"编辑"按钮跳转到编辑页面
5. **保存内容**：在编辑页面完成编辑后点击"保存"或"确存"
6. **返回列表**：点击"返回列表"按钮回到列表页面

## 数据流

### 列表页面数据
- 内容列表数据（模拟数据）
- 搜索筛选参数
- 分页信息
- 操作状态更新

### 编辑页面数据
- 模块属性数据
- 策略设置数据
- 内容管理数据
- 策略管理表格数据
- 根据URL参数加载对应的编辑数据

## 后续扩展

- [ ] 接入真实API
- [ ] 添加表单验证
- [ ] 实现图片上传功能
- [ ] 添加权限控制
- [ ] 优化用户体验
- [ ] 添加更多操作功能

## 注意事项

- 当前使用模拟数据，需要替换为真实API
- URL参数传递方式适合当前路由系统
- 编辑页面支持新增和编辑两种模式
- 所有操作都有相应的用户反馈
