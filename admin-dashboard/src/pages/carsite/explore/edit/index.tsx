import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Message,
  Grid,
  Form,
  Input,
  Select,
  Radio,
  Upload,
  Checkbox,
  DatePicker,
  Table,
  Popconfirm,
} from '@arco-design/web-react';
import {
  IconEdit,
  IconEye,
  IconCopy,
  IconCheck,
  IconClose,
  IconClockCircle,
  IconPlus,
  IconUpload,
  IconDown,
  IconUp,
  IconDelete,
  IconArrowLeft,
} from '@arco-design/web-react/icon';
import { useHistory, useLocation } from 'react-router-dom';
import qs from 'query-string';
import axios from 'axios';
import useLocale from '@/utils/useLocale';
import locale from './locale';
import styles from './style/index.module.less';

const { Title } = Typography;
const { Row, Col } = Grid;

export interface StrategyItem {
  id: string;
  name: string;
  status: string;
  upTime: string;
  downTime: string;
  creator: string;
  priority: number;
  conditions: string;
}

function ExploreEdit() {
  const t = useLocale(locale);
  const history = useHistory();
  const location = useLocation();

  // 从URL获取ID参数
  const urlParams = qs.parse(location.search);
  const itemId = urlParams.id as string;
  const isNew = itemId === 'new';

  // 模块属性状态
  const [moduleForm] = Form.useForm();
  const [moduleData, setModuleData] = useState({
    moduleId: '11',
    moduleName: '金融区导航',
    moduleVersion: '横向卡片',
  });

  // 策略管理状态
  const [strategyData, setStrategyData] = useState<StrategyItem[]>([]);
  const [strategyLoading, setStrategyLoading] = useState(false);
  const [strategyForm] = Form.useForm();

  // 内容管理状态
  const [contentData, setContentData] = useState([]);
  const [contentLoading, setContentLoading] = useState(false);
  const [contentForm] = Form.useForm();

  useEffect(() => {
    if (!isNew) {
      // 编辑模式，加载现有数据
      loadItemData(itemId);
    }
    fetchStrategyData();
    fetchContentData();
  }, [itemId, isNew]);

  // 加载编辑项数据
  function loadItemData(id: string) {
    console.log('加载编辑项数据:', id);
    // 这里可以根据ID加载具体的数据
    // 模拟加载数据
    setTimeout(() => {
      Message.success(`加载ID为 ${id} 的数据成功`);
    }, 500);
  }

  // 获取策略数据
  function fetchStrategyData() {
    setStrategyLoading(true);
    setTimeout(() => {
      const mockStrategyData: StrategyItem[] = [
        {
          id: '1',
          name: '金融区导航',
          status: '已上线',
          upTime: '2025-05-01 12:00:00',
          downTime: '2025-05-01 12:00:00',
          creator: '张三',
          priority: 1,
          conditions: '完整',
        },
        {
          id: '2',
          name: '金融区（车主）',
          status: '审核中',
          upTime: '2025-05-01 12:00:00',
          downTime: '永不下线',
          creator: '张三',
          priority: 1,
          conditions: '车主、成年人',
        },
        {
          id: '3',
          name: '金融区（新车主）',
          status: '未上线',
          upTime: '2025-05-01 12:00:00',
          downTime: '永不下线',
          creator: '张三',
          priority: 2,
          conditions: '准车主',
        },
      ];
      setStrategyData(mockStrategyData);
      setStrategyLoading(false);
    }, 500);
  }

  // 获取内容数据
  function fetchContentData() {
    setContentLoading(true);
    setTimeout(() => {
      const mockContentData = [
        {
          id: '1',
          cardTitle: 'xxxxx主题',
          content: {
            text: '输入"车主"等"车主身份"',
            toast: 'Toast提示',
            socialProof: '输入社会化证明',
            autoComplete: '输入自动完成文案内容',
          },
          images: {
            cover: '封面图',
            halfScreen: '半屏图',
            fullScreen: '全屏图(竖)',
            fullScreenH: '全屏图(横)',
          },
        },
      ];
      setContentData(mockContentData);
      setContentLoading(false);
    }, 500);
  }

  // 策略操作函数
  const handleStrategyOperation = (record: StrategyItem, type: string) => {
    switch (type) {
      case 'edit':
        Message.info(`编辑策略: ${record.name}`);
        break;
      case 'copy':
        Message.success(`复制策略: ${record.name}`);
        break;
      case 'online':
        Message.success(`上线策略: ${record.name}`);
        setStrategyData(strategyData.map(item =>
          item.id === record.id ? { ...item, status: '已上线' } : item
        ));
        break;
      case 'offline':
        Message.success(`下线策略: ${record.name}`);
        setStrategyData(strategyData.map(item =>
          item.id === record.id ? { ...item, status: '未上线' } : item
        ));
        break;
      case 'submit':
        Message.success(`提交审核: ${record.name}`);
        setStrategyData(strategyData.map(item =>
          item.id === record.id ? { ...item, status: '审核中' } : item
        ));
        break;
      case 'delete':
        Message.success(`删除策略: ${record.name}`);
        setStrategyData(strategyData.filter(item => item.id !== record.id));
        break;
    }
  };

  // 保存模块信息
  const handleSave = () => {
    Message.success('保存成功');
  };

  // 确认保存
  const handleConfirmSave = () => {
    Message.success('确认保存成功');
  };

  // 返回列表
  const handleBack = () => {
    history.push('/carsite/explore');
  };

  // 策略表格列定义
  const strategyColumns = [
    { title: '策略id', dataIndex: 'id', width: 80 },
    { title: '策略名称', dataIndex: 'name', width: 150 },
    { title: '状态', dataIndex: 'status', width: 100 },
    { title: '上下线时间', dataIndex: 'upTime', width: 150 },
    { title: '创建人', dataIndex: 'creator', width: 100 },
    { title: '优先级', dataIndex: 'priority', width: 80 },
    { title: '受众条件', dataIndex: 'conditions', width: 150 },
    {
      title: '操作',
      dataIndex: 'operations',
      width: 200,
      render: (_, record: StrategyItem) => (
        <Space size="small">
          <Button type="text" size="small" onClick={() => handleStrategyOperation(record, 'online')}>
            申请上线
          </Button>
          <Button type="text" size="small" onClick={() => handleStrategyOperation(record, 'copy')}>
            复制
          </Button>
          <Button type="text" size="small" onClick={() => handleStrategyOperation(record, 'delete')}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      {/* 页面标识 */}
      <div style={{
        background: '#ff4d4f',
        color: 'white',
        padding: '10px',
        textAlign: 'center',
        marginBottom: '16px',
        fontSize: '18px',
        fontWeight: 'bold'
      }}>
        🎉 探索页编辑页面 - ID: {itemId} {isNew ? '(新增模式)' : '(编辑模式)'}
      </div>

      {/* 页面头部 */}
      <div className={styles.pageHeader}>
        <div className={styles.breadcrumbSection}>
          <div className={styles.backButton}>
            <Button icon={<IconArrowLeft />} onClick={handleBack}>
              返回列表
            </Button>
          </div>
          <span>首页 >> 页面管理 >> 页面编辑 >> 模块内容管理</span>
          <div className={styles.pageActions}>
            <Space>
              <span>页面：</span>
              <Select defaultValue="探索" style={{ width: 100 }}>
                <Select.Option value="探索">探索</Select.Option>
                <Select.Option value="发现">发现</Select.Option>
              </Select>
              <span>模块：</span>
              <Select defaultValue="金融区" style={{ width: 100 }}>
                <Select.Option value="金融区">金融区</Select.Option>
                <Select.Option value="其他">其他</Select.Option>
              </Select>
              <Button type="primary">切换</Button>
            </Space>
          </div>
        </div>
        <div className={styles.actionButtons}>
          <Space>
            <Button onClick={handleSave}>保存</Button>
            <Button type="primary" onClick={handleConfirmSave}>确存</Button>
          </Space>
        </div>
      </div>

      {/* 模块属性 */}
      <Card className={styles.moduleCard}>
        <div className={styles.moduleHeader}>
          <Title heading={6}>模块属性</Title>
        </div>
        <div className={styles.moduleContent}>
          <Space size="large">
            <span>模块ID：{moduleData.moduleId}</span>
            <span>模块名称：{moduleData.moduleName}</span>
            <span>模版：{moduleData.moduleVersion}</span>
          </Space>
        </div>
      </Card>

      <Row gutter={16}>
        {/* 策略设置 */}
        <Col span={12}>
          <Card className={styles.strategyCard}>
            <Title heading={6}>策略设置</Title>

            {/* 策略搜索 */}
            <div className={styles.strategySearch}>
              <Form form={strategyForm} layout="inline">
                <Form.Item label="策略名称：" field="strategyName">
                  <Input placeholder="xxxxxx" style={{ width: 150 }} />
                </Form.Item>
                <Form.Item label="策略优先级：" field="priority">
                  <Select placeholder="2 优先级" style={{ width: 120 }}>
                    <Select.Option value="1">1 优先级</Select.Option>
                    <Select.Option value="2">2 优先级</Select.Option>
                    <Select.Option value="3">3 优先级</Select.Option>
                  </Select>
                </Form.Item>
              </Form>

              <div className={styles.strategyFilters}>
                <Title heading={6}>受众条件：</Title>
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Checkbox.Group>
                      <Checkbox value="all">全部</Checkbox>
                      <Checkbox value="owner">车主</Checkbox>
                    </Checkbox.Group>
                  </Col>
                  <Col span={12}>
                    <Checkbox.Group>
                      <Checkbox value="adult">成年人</Checkbox>
                      <Checkbox value="minor">未成年</Checkbox>
                    </Checkbox.Group>
                  </Col>
                </Row>
              </div>

              <div className={styles.timeSettings}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="上线时间：">
                      <DatePicker
                        showTime
                        placeholder="2025-05-11 00:00:00"
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="下线时间：">
                      <DatePicker
                        showTime
                        placeholder="2025-05-17 00:00:00"
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </div>

              <div className={styles.sortSettings}>
                <Title heading={6}>样式设置：</Title>
                <div className={styles.sortPreview}>
                  <div className={styles.previewPlaceholder}>
                    样式预览区域
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </Col>

        {/* 内容管理 */}
        <Col span={12}>
          <Card className={styles.contentCard}>
            <Title heading={6}>内容管理</Title>

            <div className={styles.contentForm}>
              <Form form={contentForm} layout="vertical">
                <Form.Item label="卡片标题：" field="cardTitle">
                  <Input placeholder="xxxxx主题" />
                </Form.Item>

                <Form.Item label="营销设置：">
                  <div className={styles.marketingSettings}>
                    <div className={styles.marketingItem}>
                      <span>文字：</span>
                      <Input placeholder='输入"车主"等"车主身份"' style={{ width: 200 }} />
                    </div>
                    <div className={styles.marketingItem}>
                      <span>文案：</span>
                      <Radio.Group>
                        <Radio value="toast">Toast提示</Radio>
                      </Radio.Group>
                      <Input placeholder="输入自定义文案内容" style={{ width: 200, marginLeft: 8 }} />
                      <Button type="link">预览</Button>
                    </div>
                    <div className={styles.marketingItem}>
                      <span></span>
                      <Radio.Group>
                        <Radio value="social">社会化证明</Radio>
                      </Radio.Group>
                      <Input placeholder="输入社会化证明" style={{ width: 200, marginLeft: 8 }} />
                      <Button type="link">预览</Button>
                    </div>
                    <div className={styles.marketingItem}>
                      <span></span>
                      <Radio.Group>
                        <Radio value="auto">自动完成提醒</Radio>
                      </Radio.Group>
                      <Input placeholder="输入自动完成提醒内容" style={{ width: 200, marginLeft: 8 }} />
                      <Button type="link">预览</Button>
                    </div>
                  </div>
                </Form.Item>

                <Form.Item label="内容设置：">
                  <div className={styles.contentSettings}>
                    <div className={styles.uploadSection}>
                      <span>附图：</span>
                      <Button icon={<IconUpload />}>上传</Button>
                      <Button>删除</Button>
                      <Button>替换</Button>
                      <Button icon={<IconDown />}>下移</Button>
                    </div>

                    <div className={styles.imageGrid}>
                      <Row gutter={8}>
                        <Col span={6}>
                          <div className={styles.imageSlot}>
                            <div className={styles.imagePlaceholder}>封面图</div>
                          </div>
                        </Col>
                        <Col span={6}>
                          <div className={styles.imageSlot}>
                            <div className={styles.imagePlaceholder}>半屏图</div>
                          </div>
                        </Col>
                        <Col span={6}>
                          <div className={styles.imageSlot}>
                            <div className={styles.imagePlaceholder}>全屏图(竖)</div>
                          </div>
                        </Col>
                        <Col span={6}>
                          <div className={styles.imageSlot}>
                            <div className={styles.imagePlaceholder}>全屏图(横)</div>
                          </div>
                        </Col>
                      </Row>
                    </div>

                    <div className={styles.marketingSettings}>
                      <div className={styles.marketingItem}>
                        <span>文案：</span>
                        <Radio.Group>
                          <Radio value="toast2">Toast提示</Radio>
                        </Radio.Group>
                        <Input placeholder="输入自定义文案内容" style={{ width: 200, marginLeft: 8 }} />
                        <Button type="link">预览</Button>
                      </div>
                      <div className={styles.marketingItem}>
                        <span></span>
                        <Radio.Group>
                          <Radio value="social2">社会化证明</Radio>
                        </Radio.Group>
                        <Input placeholder="输入社会化证明" style={{ width: 200, marginLeft: 8 }} />
                        <Button type="link">预览</Button>
                      </div>
                      <div className={styles.marketingItem}>
                        <span></span>
                        <Radio.Group>
                          <Radio value="auto2">自动完成提醒</Radio>
                        </Radio.Group>
                        <Input placeholder="输入自动完成提醒内容" style={{ width: 200, marginLeft: 8 }} />
                        <Button type="link">预览</Button>
                      </div>
                    </div>

                    <Button type="primary" block style={{ marginTop: 16 }}>
                      + 添加一项
                    </Button>
                  </div>
                </Form.Item>
              </Form>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 策略管理表格 */}
      <Card style={{ marginTop: 16 }}>
        <div className={styles.strategyTableHeader}>
          <Title heading={6}>策略管理</Title>
          <div className={styles.strategyTableFilters}>
            <Form layout="inline">
              <Form.Item label="状态">
                <Select placeholder="全部" style={{ width: 120 }}>
                  <Select.Option value="">全部</Select.Option>
                  <Select.Option value="online">已上线</Select.Option>
                  <Select.Option value="offline">未上线</Select.Option>
                  <Select.Option value="review">审核中</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item label="群体">
                <Select placeholder="全部" style={{ width: 120 }}>
                  <Select.Option value="">全部</Select.Option>
                  <Select.Option value="owner">车主</Select.Option>
                  <Select.Option value="potential">准车主</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Button type="primary">查询</Button>
              </Form.Item>
            </Form>
          </div>
        </div>

        <div style={{ marginBottom: 16 }}>
          <Button type="primary" icon={<IconPlus />}>
            添加
          </Button>
        </div>

        <Table
          rowKey="id"
          loading={strategyLoading}
          columns={strategyColumns}
          data={strategyData}
          pagination={false}
        />

        <div className={styles.tableActions}>
          <Space>
            <Button>批量提交审核</Button>
            <Button>批量删除</Button>
          </Space>
        </div>
      </Card>
    </div>
  );
}

export default ExploreEdit;
