.container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  .breadcrumbSection {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .backButton {
      margin-bottom: 8px;
    }
    
    .pageActions {
      display: flex;
      align-items: center;
      gap: 8px;
      
      span {
        font-size: 14px;
        color: #666;
      }
    }
  }
  
  .actionButtons {
    display: flex;
    align-items: center;
  }
}

.moduleCard {
  margin-bottom: 16px;
  border: 2px solid #ff7d00;
  
  .moduleHeader {
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 8px;
    margin-bottom: 16px;
  }
  
  .moduleContent {
    padding: 8px 0;
  }
}

.strategyCard {
  height: fit-content;
  
  .strategySearch {
    .strategyFilters {
      margin: 16px 0;
      
      h6 {
        margin-bottom: 8px;
      }
    }
    
    .timeSettings {
      margin: 16px 0;
    }
    
    .sortSettings {
      margin: 16px 0;
      
      .previewPlaceholder {
        height: 120px;
        border: 1px dashed #d9d9d9;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        background-color: #fafafa;
      }
    }
  }
}

.contentCard {
  height: fit-content;
  
  .contentForm {
    .marketingSettings {
      .marketingItem {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        gap: 8px;
        
        span {
          min-width: 40px;
        }
      }
    }
    
    .contentSettings {
      .uploadSection {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        
        span {
          min-width: 40px;
        }
      }
      
      .imageGrid {
        margin: 16px 0;
        
        .imageSlot {
          .imagePlaceholder {
            height: 80px;
            border: 1px dashed #d9d9d9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #999;
            background-color: #fafafa;
          }
        }
      }
    }
  }
}

.strategyTableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .strategyTableFilters {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

.tableActions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e5e5;
}

.search-form-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background-color: var(--color-fill-2);
  border-radius: 4px;

  .search-form {
    flex: 1;
    margin-right: 20px;
  }

  .right-button {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 120px;

    button {
      width: 100%;
    }
  }
}

.button-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.content-type {
  display: flex;
  align-items: center;
  gap: 8px;

  svg {
    width: 16px;
    height: 16px;
  }
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 4px;
}

.operations {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.no-data {
  text-align: center;
  color: var(--color-text-3);
  padding: 40px 0;
}
