const i18n = {
  'zh-CN': {
    'exploreList.title': '探索页管理',
    'exploreList.search.id': '内容ID',
    'exploreList.search.title': '标题',
    'exploreList.search.contentType': '内容类型',
    'exploreList.search.status': '状态',
    'exploreList.search.author': '作者',
    'exploreList.search.placeholder.id': '请输入内容ID',
    'exploreList.search.placeholder.title': '请输入内容标题',
    'exploreList.search.placeholder.author': '请输入作者姓名',
    'exploreList.form.search': '搜索',
    'exploreList.form.reset': '重置',
    'exploreList.operations.add': '新增内容',
    'exploreList.operations.import': '批量导入',
    'exploreList.operations.export': '导出数据',
    'exploreList.operations.view': '查看',
    'exploreList.operations.edit': '编辑',
    'exploreList.operations.copy': '复制',
    'exploreList.operations.online': '上线',
    'exploreList.operations.offline': '下线',
    'exploreList.operations.submit': '提交审核',
  },
  'en-US': {
    'exploreList.title': 'Explore Management',
    'exploreList.search.id': 'Content ID',
    'exploreList.search.title': 'Title',
    'exploreList.search.contentType': 'Content Type',
    'exploreList.search.status': 'Status',
    'exploreList.search.author': 'Author',
    'exploreList.search.placeholder.id': 'Please enter content ID',
    'exploreList.search.placeholder.title': 'Please enter title',
    'exploreList.search.placeholder.author': 'Please enter author name',
    'exploreList.form.search': 'Search',
    'exploreList.form.reset': 'Reset',
    'exploreList.operations.add': 'Add Content',
    'exploreList.operations.import': 'Batch Import',
    'exploreList.operations.export': 'Export Data',
    'exploreList.operations.view': 'View',
    'exploreList.operations.edit': 'Edit',
    'exploreList.operations.copy': 'Copy',
    'exploreList.operations.online': 'Online',
    'exploreList.operations.offline': 'Offline',
    'exploreList.operations.submit': 'Submit Review',
  },
};

export default i18n;
