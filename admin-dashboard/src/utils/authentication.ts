/**
 * { data-analysis:  ['read', 'write'] }
 */

export type UserPermission = Record<string, string[]>;

type Auth = {
  resource: string | RegExp;
  actions?: string[];
};

export interface AuthParams {
  requiredPermissions?: Array<Auth>;
  oneOfPerm?: boolean;
}

const judge = (actions: string[], perm: string[]) => {
  if (!perm || !perm.length) {
    return false;
  }

  if (perm.join('') === '*') {
    return true;
  }

  return actions.every((action) => perm.includes(action));
};

const auth = (params: Auth, userPermission: UserPermission) => {
  const { resource, actions = [] } = params;
  if (resource instanceof RegExp) {
    const permKeys = Object.keys(userPermission);
    const matchPermissions = permKeys.filter((item) => item.match(resource));
    if (!matchPermissions.length) {
      return false;
    }
    return matchPermissions.every((key) => {
      const perm = userPermission[key];
      return judge(actions, perm);
    });
  }

  const perm = userPermission[resource];
  return judge(actions, perm);
};

export default (params: AuthParams, userPermission: UserPermission) => {
  const { requiredPermissions, oneOfPerm } = params;

  // 调试信息
  console.log('权限验证参数:', params);
  console.log('用户权限:', userPermission);

  if (Array.isArray(requiredPermissions) && requiredPermissions.length) {
    let count = 0;
    for (const rp of requiredPermissions) {
      const hasAuth = auth(rp, userPermission);
      console.log(`验证权限: ${rp.resource}:${rp.actions}, 结果: ${hasAuth}`);
      if (hasAuth) {
        count++;
      }
    }
    const result = oneOfPerm ? count > 0 : count === requiredPermissions.length;
    console.log(
      `权限验证结果: ${result}, 需要满足所有权限: ${!oneOfPerm}, 满足权限数: ${count}, 总权限数: ${
        requiredPermissions.length
      }`
    );
    return result;
  }
  return true;
};
