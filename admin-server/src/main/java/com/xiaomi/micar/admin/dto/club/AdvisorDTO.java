package com.xiaomi.micar.admin.dto.club;

import lombok.Data;

import java.util.Date;

/**
 * 顾问 DTO
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
public class AdvisorDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 顾问姓名
     */
    private String name;

    /**
     * 企微二维码链接
     */
    private String wechatQr;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 服务客户数量
     */
    private Integer assignedCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
