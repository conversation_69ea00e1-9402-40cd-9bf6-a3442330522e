package com.xiaomi.micar.admin.dto.club;

import lombok.Data;

import java.util.List;

/**
 * 白名单更新 DTO
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
public class WhitelistUpdateDTO {

    /**
     * 需要添加到白名单的MID列表
     */
    private List<String> midsToAdd;

    /**
     * 需要从白名单移除的MID列表
     */
    private List<String> midsToRemove;

    /**
     * 操作类型 (add:添加, remove:移除, replace:替换)
     */
    private String operationType;

    /**
     * 操作原因
     */
    private String reason;
}
