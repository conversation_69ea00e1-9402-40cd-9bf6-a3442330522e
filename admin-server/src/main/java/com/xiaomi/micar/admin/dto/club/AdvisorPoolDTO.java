package com.xiaomi.micar.admin.dto.club;

import lombok.Data;

import java.util.Date;

/**
 * 顾问资源池 DTO
 *
 * <AUTHOR>
 * @since 2023/05/07
 */
@Data
public class AdvisorPoolDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 顾问姓名
     */
    private String name;

    /**
     * 企微二维码链接
     */
    private String wechat_qr;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 服务客户数量
     */
    private Integer assigned_count;

    /**
     * 创建时间
     */
    private Date create_time;

    /**
     * 更新时间
     */
    private Date update_time;
}
