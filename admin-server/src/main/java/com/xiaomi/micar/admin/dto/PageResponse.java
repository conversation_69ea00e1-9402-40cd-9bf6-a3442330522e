package com.xiaomi.micar.admin.dto;

import lombok.Data;

import java.util.List;

/**
 * 分页响应 DTO
 *
 * <AUTHOR>
 * @since 2023/05/07
 */
@Data
public class PageResponse<T> {

    /**
     * 总记录数
     */
    private long total;

    /**
     * 当前页数据
     */
    private List<T> list;

    /**
     * 构造方法
     *
     * @param total 总记录数
     * @param list  当前页数据
     */
    public PageResponse(long total, List<T> list) {
        this.total = total;
        this.list = list;
    }
}
