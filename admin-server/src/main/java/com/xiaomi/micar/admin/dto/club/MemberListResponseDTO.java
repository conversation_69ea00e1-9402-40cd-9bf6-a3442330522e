package com.xiaomi.micar.admin.dto.club;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 会员列表响应 DTO
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
public class MemberListResponseDTO {

    /**
     * 总数
     */
    private Long total;

    /**
     * 会员列表
     */
    private List<MemberInfo> list;

    @Data
    public static class MemberInfo {
        /**
         * 会员MID
         */
        private String mid;

        /**
         * 订单号
         */
        private String orderNumber;

        /**
         * 会员号
         */
        private String memberNumber;

        /**
         * 姓名
         */
        private String name;

        /**
         * 生日
         */
        private String birthday;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 顾问姓名
         */
        private String advisor;

        /**
         * 入会时间
         */
        private Date joinTime;

        /**
         * 更新时间
         */
        private Date updateTime;

        /**
         * 会员状态 (0:正常, 1:已退会, 2:退款中)
         */
        private Integer status;

        /**
         * 入会礼状态 (0:未领取, 1:已领取)
         */
        private Integer giftReceived;

        /**
         * 入会礼领取时间
         */
        private Date giftReceivedTime;
    }
}
