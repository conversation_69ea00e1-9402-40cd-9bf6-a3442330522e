package com.xiaomi.micar.admin.dto.club;

import lombok.Data;

import java.util.List;

/**
 * 顾问转移 DTO
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
public class AdvisorTransferDTO {

    /**
     * 源顾问ID
     */
    private Long sourceAdvisorId;

    /**
     * 目标顾问ID
     */
    private Long targetAdvisorId;

    /**
     * 需要转移的会员MID列表
     */
    private List<String> memberMids;

    /**
     * 转移原因
     */
    private String reason;
}
