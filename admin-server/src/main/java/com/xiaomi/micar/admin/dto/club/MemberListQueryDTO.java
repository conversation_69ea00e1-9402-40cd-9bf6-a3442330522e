package com.xiaomi.micar.admin.dto.club;

import lombok.Data;

/**
 * 会员列表查询 DTO
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
public class MemberListQueryDTO {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 会员号
     */
    private String memberNumber;

    /**
     * 顾问姓名
     */
    private String advisorName;

    /**
     * 顾问ID
     */
    private Long advisorId;

    /**
     * 会员状态 (1:有效, 0:作废, 2:作废中)
     */
    private String status;

    /**
     * 入会礼状态 (0:未领取, 1:已领取)
     */
    private String giftStatus;

    /**
     * 入会时间开始
     */
    private String joinTimeStart;

    /**
     * 入会时间结束
     */
    private String joinTimeEnd;

    /**
     * 会员MID
     */
    private String mid;

    /**
     * 订单号
     */
    private String orderNumber;
}
