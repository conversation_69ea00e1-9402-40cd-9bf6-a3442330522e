package com.xiaomi.micar.admin.dto.club;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 白名单响应 DTO
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
public class WhitelistResponseDTO {

    /**
     * 总数
     */
    private Long total;

    /**
     * 白名单列表
     */
    private List<WhitelistInfo> list;

    @Data
    public static class WhitelistInfo {
        /**
         * 主键ID
         */
        private Long id;

        /**
         * 会员MID
         */
        private String mid;

        /**
         * 会员姓名
         */
        private String memberName;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 添加原因
         */
        private String reason;

        /**
         * 操作人
         */
        private String operator;

        /**
         * 创建时间
         */
        private Date createTime;

        /**
         * 更新时间
         */
        private Date updateTime;

        /**
         * 状态 (1:有效, 0:无效)
         */
        private Integer status;
    }
}
