package com.xiaomi.micar.admin.dto.club;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 操作日志响应 DTO
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
public class OperationLogResponseDTO {

    /**
     * 总数
     */
    private Long total;

    /**
     * 操作日志列表
     */
    private List<OperationLogInfo> list;

    @Data
    public static class OperationLogInfo {
        /**
         * 日志ID
         */
        private Long id;

        /**
         * 操作类型
         */
        private String operationType;

        /**
         * 操作模块
         */
        private String operationModule;

        /**
         * 操作描述
         */
        private String operationDesc;

        /**
         * 操作人
         */
        private String operator;

        /**
         * 操作时间
         */
        private Date operationTime;

        /**
         * 操作结果 (true:成功, false:失败)
         */
        private Boolean operationResult;

        /**
         * 详细信息
         */
        private String details;
    }
}
