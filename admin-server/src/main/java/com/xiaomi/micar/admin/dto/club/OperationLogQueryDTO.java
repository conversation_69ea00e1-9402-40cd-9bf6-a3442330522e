package com.xiaomi.micar.admin.dto.club;

import lombok.Data;

import java.util.Date;

/**
 * 操作日志查询 DTO
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
public class OperationLogQueryDTO {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作模块
     */
    private String operationModule;

    /**
     * 操作描述关键字
     */
    private String operationDesc;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间开始
     */
    private Date operationTimeStart;

    /**
     * 操作时间结束
     */
    private Date operationTimeEnd;

    /**
     * 操作结果 (true:成功, false:失败)
     */
    private Boolean operationResult;
}
