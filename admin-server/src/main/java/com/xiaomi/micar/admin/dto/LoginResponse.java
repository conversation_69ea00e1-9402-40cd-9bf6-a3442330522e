package com.xiaomi.micar.admin.dto;

import lombok.Data;

/**
 * 登录响应
 *
 * <AUTHOR>
 * @since 2023/05/07
 */
@Data
public class LoginResponse {
    
    /**
     * 状态：ok-成功，error-失败
     */
    private String status;
    
    /**
     * 错误信息
     */
    private String msg;
    
    /**
     * 创建成功响应
     *
     * @return 成功响应
     */
    public static LoginResponse success() {
        LoginResponse response = new LoginResponse();
        response.setStatus("ok");
        return response;
    }
    
    /**
     * 创建错误响应
     *
     * @param msg 错误信息
     * @return 错误响应
     */
    public static LoginResponse error(String msg) {
        LoginResponse response = new LoginResponse();
        response.setStatus("error");
        response.setMsg(msg);
        return response;
    }
}
